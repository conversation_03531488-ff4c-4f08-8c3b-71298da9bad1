package com.greenterp

import android.content.Context
import android.util.Log
import com.greenterp.data.GlossaryItem
import com.greenterp.network.ApiService
import com.greenterp.utils.SettingsManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 词汇表管理器
 * 负责词汇表数据的获取、存储和管理
 */
class GlossaryManager(private val context: Context) {
    
    companion object {
        private const val TAG = "GlossaryManager"
    }
    
    private val settingsManager = SettingsManager(context)
    private val apiService = ApiService.getInstance().apply {
        initUserManager(context)
    }
    
    // 当前词汇表数据
    private val _glossaryItems = MutableStateFlow<List<GlossaryItem>>(emptyList())
    val glossaryItems: StateFlow<List<GlossaryItem>> = _glossaryItems.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    /**
     * 根据场景ID列表获取词汇表
     */
    suspend fun loadGlossaryBySceneIds(sceneIds: List<Int>): Boolean {
        if (sceneIds.isEmpty()) {
            Log.w(TAG, "场景ID列表为空，清空词汇表数据")
            _glossaryItems.value = emptyList()
            _errorMessage.value = null
            return true
        }
        
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val userId = apiService.getCurrentUserId()
            if (userId.isEmpty()) {
                _errorMessage.value = "用户未登录"
                Log.e(TAG, "用户未登录，无法获取词汇表")
                return false
            }

            Log.d(TAG, "请求词汇表，场景IDs: $sceneIds, 用户ID: $userId")

            val glossaryResponse = apiService.getGlossaryBySceneIds(sceneIds, userId)

            if (glossaryResponse != null && glossaryResponse.success) {
                val items = glossaryResponse.data.data
                _glossaryItems.value = items
                Log.d(TAG, "成功获取词汇表，共 ${items.size} 条记录")

                // 打印词汇表内容用于调试
                items.forEach { item ->
                    Log.d(TAG, "词汇: ${item.glossaryA} -> ${item.glossaryB}")
                }

                true
            } else {
                val errorMsg = glossaryResponse?.msg ?: "获取词汇表失败"
                _errorMessage.value = errorMsg
                Log.e(TAG, "获取词汇表失败: $errorMsg")
                false
            }
        } catch (e: Exception) {
            val errorMsg = "获取词汇表异常: ${e.message}"
            _errorMessage.value = errorMsg
            Log.e(TAG, errorMsg, e)
            false
        } finally {
            _isLoading.value = false
        }
    }
    
    /**
     * 清空词汇表数据
     */
    fun clearGlossary() {
        _glossaryItems.value = emptyList()
        _errorMessage.value = null
        Log.d(TAG, "清空词汇表数据")
    }
    
    /**
     * 清空错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 根据原文查找译文
     * 用于ASR识别时的词汇替换
     */
    fun findTranslation(originalText: String): String? {
        val items = _glossaryItems.value
        return items.find { it.glossaryA.equals(originalText, ignoreCase = true) }?.glossaryB
    }
    
    /**
     * 获取所有原文词汇（用于ASR识别时的匹配）
     */
    fun getAllOriginalTexts(): List<String> {
        return _glossaryItems.value.map { it.glossaryA }
    }
    
    /**
     * 获取当前词汇表数量
     */
    fun getGlossaryCount(): Int {
        return _glossaryItems.value.size
    }

    /**
     * 处理ASR识别文本，匹配词汇表并添加高亮标记
     * 参考JS算法，使用安全替换避免嵌套括号问题
     * @param text ASR识别的原始文本
     * @return 处理后的文本，包含词汇表匹配和标记信息
     */
    fun processAsrText(text: String): ProcessedText {
        val items = _glossaryItems.value
        if (items.isEmpty() || text.isBlank()) {
            return ProcessedText(text, emptyList())
        }

        var processedText = text
        val highlights = mutableListOf<TextHighlight>()

        // 按词汇长度降序排序，优先匹配长词汇，避免短词汇覆盖长词汇
        val sortedItems = items.sortedByDescending { maxOf(it.glossaryA.length, it.glossaryB.length) }

        for (item in sortedItems) {
            // 先尝试匹配glossaryA
            val glossaryAResult = safeGlossaryReplace(processedText, item.glossaryA, item.glossaryB, true)

            if (glossaryAResult.text != processedText) {
                // glossaryA匹配成功
                processedText = glossaryAResult.text
                highlights.addAll(glossaryAResult.highlights)
            } else {
                // glossaryA没有匹配，尝试匹配glossaryB
                val glossaryBResult = safeGlossaryReplace(processedText, item.glossaryB, item.glossaryA, false)

                if (glossaryBResult.text != processedText) {
                    // glossaryB匹配成功
                    processedText = glossaryBResult.text
                    highlights.addAll(glossaryBResult.highlights)
                }
            }
        }

        return ProcessedText(processedText, highlights.sortedBy { it.start })
    }

    /**
     * 安全的词汇表替换，避免对已经处理过的部分再次处理
     * 参考JS的safeGlossaryReplace函数
     */
    private fun safeGlossaryReplace(
        text: String,
        searchWord: String,
        replaceWord: String,
        isGlossaryA: Boolean
    ): GlossaryReplaceResult {
        val highlights = mutableListOf<TextHighlight>()

        // 使用正则表达式进行全词匹配，但排除已经在括号中的词汇
        val pattern = "\\b${Regex.escape(searchWord)}\\b(?![^()]*\\))".toRegex(RegexOption.IGNORE_CASE)

        var processedText = text
        var offset = 0

        pattern.findAll(text).forEach { matchResult ->
            val matchStart = matchResult.range.first + offset
            val matchEnd = matchResult.range.last + 1 + offset
            val originalWord = matchResult.value
            val replacement = "$originalWord($replaceWord)"

            // 执行替换
            processedText = processedText.replaceRange(matchStart, matchEnd, replacement)

            // 记录高亮信息
            highlights.add(TextHighlight(
                start = matchStart,
                end = matchStart + replacement.length,
                originalWord = originalWord,
                translatedWord = replaceWord,
                isGlossaryA = isGlossaryA
            ))

            // 更新偏移量
            offset += replacement.length - originalWord.length
        }

        return GlossaryReplaceResult(processedText, highlights)
    }

}

/**
 * 词汇表替换结果
 */
data class GlossaryReplaceResult(
    val text: String,
    val highlights: List<TextHighlight>
)

/**
 * 处理后的文本结果
 */
data class ProcessedText(
    val text: String,
    val highlights: List<TextHighlight>
)

/**
 * 文本高亮信息
 */
data class TextHighlight(
    val start: Int,
    val end: Int,
    val originalWord: String,
    val translatedWord: String,
    val isGlossaryA: Boolean // true表示匹配的是glossaryA，false表示匹配的是glossaryB
)
