package com.greenterp.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.greenterp.network.ApiService
import com.greenterp.network.LoginResult
import com.greenterp.data.CredentialsManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 登录ViewModel
 */
class LoginViewModel : ViewModel() {
    private val apiService = ApiService.getInstance()
    private var credentialsManager: CredentialsManager? = null

    // 登录状态
    private val _loginState = MutableStateFlow<LoginResult>(LoginResult.Loading)
    val loginState: StateFlow<LoginResult> = _loginState.asStateFlow()

    // 输入字段状态
    private val _email = MutableStateFlow("")
    val email: StateFlow<String> = _email.asStateFlow()

    private val _password = MutableStateFlow("")
    val password: StateFlow<String> = _password.asStateFlow()

    // 记住密码状态
    private val _rememberPassword = MutableStateFlow(false)
    val rememberPassword: StateFlow<Boolean> = _rememberPassword.asStateFlow()
    
    // Initial state set to non-loading state
    init {
        _loginState.value = LoginResult.Success(
            com.greenterp.network.LoginResponse(
                code = 0,
                time = 0L,
                msg = "Please enter login information",
                success = false,
                data = null
            )
        )
    }
    
    /**
     * 更新邮箱
     */
    fun updateEmail(newEmail: String) {
        _email.value = newEmail
    }
    
    /**
     * 更新密码
     */
    fun updatePassword(newPassword: String) {
        _password.value = newPassword
    }

    /**
     * 更新记住密码状态
     */
    fun updateRememberPassword(remember: Boolean) {
        _rememberPassword.value = remember
    }

    /**
     * 初始化凭据管理器并加载保存的凭据
     */
    fun initCredentialsManager(context: Context) {
        credentialsManager = CredentialsManager.getInstance(context)
        loadSavedCredentials()
    }

    /**
     * 加载保存的凭据
     */
    private fun loadSavedCredentials() {
        credentialsManager?.let { manager ->
            val savedEmail = manager.getSavedEmail()
            val savedPassword = manager.getSavedPassword()
            val rememberPasswordEnabled = manager.isRememberPasswordEnabled()

            if (savedEmail.isNotEmpty()) {
                _email.value = savedEmail
            }

            if (rememberPasswordEnabled && savedPassword.isNotEmpty()) {
                _password.value = savedPassword
            }

            _rememberPassword.value = rememberPasswordEnabled
        }
    }
    
    /**
     * 执行登录
     */
    fun login() {
        val emailValue = _email.value.trim()
        val passwordValue = _password.value.trim()

        // Input validation
        if (emailValue.isEmpty()) {
            _loginState.value = LoginResult.Error("Please enter email address")
            return
        }

        if (passwordValue.isEmpty()) {
            _loginState.value = LoginResult.Error("Please enter password")
            return
        }

        if (!isValidEmail(emailValue)) {
            _loginState.value = LoginResult.Error("Please enter a valid email address")
            return
        }

        // 开始登录
        _loginState.value = LoginResult.Loading

        viewModelScope.launch {
            val result = apiService.login(emailValue, passwordValue)
            _loginState.value = result

            // 如果登录成功，保存凭据
            if (result is LoginResult.Success && result.response.success) {
                credentialsManager?.saveCredentials(
                    email = emailValue,
                    password = passwordValue,
                    rememberPassword = _rememberPassword.value
                )
            }
        }
    }
    
    /**
     * Reset login state
     */
    fun resetLoginState() {
        _loginState.value = LoginResult.Success(
            com.greenterp.network.LoginResponse(
                code = 0,
                time = 0L,
                msg = "",
                success = false,
                data = null
            )
        )
    }
    
    /**
     * 简单的邮箱格式验证
     */
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
