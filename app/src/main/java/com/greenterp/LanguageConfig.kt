package com.greenterp

data class LanguageItem(
    val show: String,
    val code: String,
    var isShow: Boolean = false,  // 当前是否选中
    val main: Boolean = true      // 是否为主语言（用于区分主次语言）
)

// 服务类型数据类
data class ServiceType(
    val type: String,        // 内部类型标识
    val displayName: String, // 显示名称
    val languageList: List<LanguageItem> // 支持的语言列表
)

// 服务类型枚举
enum class AsrServiceType(val type: String) {
    ONLINE_1("online_1"),           // Online-1 使用Azure实现
    ONLINE_ZH_EN("online_zh_en")    // Online-ZH/EN only 使用讯飞实现
}

object LanguageConfig {
    // Online-1 服务支持的完整语言列表
    val fullLanguageList = listOf(
        LanguageItem("Arabic-Saudi Arabia", "ar-SA"),
        LanguageItem("Catalan-Spain", "ca-ES"),
        LanguageItem("Czech-Czech Republic", "cs-CZ"),
        LanguageItem("Danish-Denmark", "da-DK"),
        LanguageItem("German-Austria", "de-AT"),
        LanguageItem("German-Switzerland", "de-CH"),
        LanguageItem("German-Germany", "de-DE"),
        LanguageItem("Greek-Greece", "el-GR"),
        LanguageItem("English-United Arab Emirates", "en-AE"),
        LanguageItem("English-Australia", "en-AU"),
        LanguageItem("English-Canada", "en-CA"),
        LanguageItem("English-United Kingdom", "en-GB"),
        LanguageItem("English-Indonesia", "en-ID"),
        LanguageItem("English-Ireland", "en-IE"),
        LanguageItem("English-India", "en-IN"),
        LanguageItem("English-New Zealand", "en-NZ"),
        LanguageItem("English-Philippines", "en-PH"),
        LanguageItem("English-Saudi Arabia", "en-SA"),
        LanguageItem("English-Singapore", "en-SG"),
        LanguageItem("English-United States", "en-US"),
        LanguageItem("English-South Africa", "en-ZA"),
        LanguageItem("Spanish-Latin America", "es-419"),
        LanguageItem("Spanish-Chile", "es-CL"),
        LanguageItem("Spanish-Colombia", "es-CO"),
        LanguageItem("Spanish-Spain", "es-ES"),
        LanguageItem("Spanish-Mexico", "es-MX"),
        LanguageItem("Spanish-United States", "es-US"),
        LanguageItem("Finnish-Finland", "fi-FI"),
        LanguageItem("French-Belgium", "fr-BE"),
        LanguageItem("French-Canada", "fr-CA"),
        LanguageItem("French-Switzerland", "fr-CH"),
        LanguageItem("French-France", "fr-FR"),
        LanguageItem("Hebrew-Israel", "he-IL"),
        LanguageItem("Hindi-India", "hi-IN"),
        LanguageItem("Hindi-India (Transliterated)", "hi-IN-translit"),
        LanguageItem("Hindi-Latin script", "hi-Latn"),
        LanguageItem("Croatian-Croatia", "hr-HR"),
        LanguageItem("Hungarian-Hungary", "hu-HU"),
        LanguageItem("Indonesian-Indonesia", "id-ID"),
        LanguageItem("Italian-Switzerland", "it-CH"),
        LanguageItem("Italian-Italy", "it-IT"),
        LanguageItem("Japanese-Japan", "ja-JP"),
        LanguageItem("Korean-South Korea", "ko-KR"),
        LanguageItem("Malay-Malaysia", "ms-MY"),
        LanguageItem("Norwegian Bokmål-Norway", "nb-NO"),
        LanguageItem("Dutch-Belgium", "nl-BE"),
        LanguageItem("Dutch-Netherlands", "nl-NL"),
        LanguageItem("Polish-Poland", "pl-PL"),
        LanguageItem("Portuguese-Brazil", "pt-BR"),
        LanguageItem("Portuguese-Portugal", "pt-PT"),
        LanguageItem("Romanian-Romania", "ro-RO"),
        LanguageItem("Russian-Russia", "ru-RU"),
        LanguageItem("Slovak-Slovakia", "sk-SK"),
        LanguageItem("Swedish-Sweden", "sv-SE"),
        LanguageItem("Thai-Thailand", "th-TH"),
        LanguageItem("Turkish-Turkey", "tr-TR"),
        LanguageItem("Ukrainian-Ukraine", "uk-UA"),
        LanguageItem("Vietnamese-Vietnam", "vi-VN"),
        LanguageItem("Wu Chinese-China", "wuu-CN"),
        LanguageItem("Cantonese-China", "yue-CN"),
        LanguageItem("Chinese-Mandarin", "zh-CN"),
        LanguageItem("Chinese-Hong Kong", "zh-HK"),
        LanguageItem("Chinese-Taiwan", "zh-TW")
    )

    // Online-ZH/EN only 服务支持的限制语言列表
    val limitedLanguageList = listOf(
        LanguageItem("English-United States", "en-US"),
        LanguageItem("Chinese-Mandarin", "zh-CN")
    )

    // SODA 语言列表（备用）
    val sodaLanguageList = listOf(
        LanguageItem("German-Germany", "de-DE"),
        LanguageItem("English-United States", "en-US"),
        LanguageItem("Spanish-Spain", "es-ES"),
        LanguageItem("French-France", "fr-FR"),
        LanguageItem("Hindi-India", "hi-IN"),
        LanguageItem("Indonesian-Indonesia", "id-ID"),
        LanguageItem("Italian-Italy", "it-IT"),
        LanguageItem("Japanese-Japan", "ja-JP"),
        LanguageItem("Korean-South Korea", "ko-KR"),
        LanguageItem("Polish-Poland", "pl-PL"),
        LanguageItem("Portuguese-Brazil", "pt-BR"),
        LanguageItem("Thai-Thailand", "th-TH"),
        LanguageItem("Turkish-Turkey", "tr-TR"),
        LanguageItem("Chinese-Mandarin", "zh-CN")
    )

    // 服务类型配置
    val serviceTypes = listOf(
        ServiceType(
            type = AsrServiceType.ONLINE_1.type,
            displayName = "Online-1",
            languageList = fullLanguageList
        ),
        ServiceType(
            type = AsrServiceType.ONLINE_ZH_EN.type,
            displayName = "Online-ZH/EN only",
            languageList = limitedLanguageList
        )
    )

    // 服务类型显示名称列表（用于UI显示）
    val serviceDisplayNames = serviceTypes.map { it.displayName }

    // 根据显示名称查找语言代码
    fun getLanguageCode(displayName: String, languageList: List<LanguageItem>): String? {
        return languageList.find { it.show == displayName }?.code
    }

    // 根据语言代码查找显示名称
    fun getLanguageDisplay(code: String, languageList: List<LanguageItem>): String? {
        return languageList.find { it.code == code }?.show
    }

    // 根据显示名称获取服务类型配置
    fun getServiceTypeByDisplayName(displayName: String): ServiceType? {
        return serviceTypes.find { it.displayName == displayName }
    }

    // 根据类型标识获取服务类型配置
    fun getServiceTypeByType(type: String): ServiceType? {
        return serviceTypes.find { it.type == type }
    }

    // 获取当前选择语言的代码（用于API调用）
    fun getCurrentLanguageCode(displayName: String, serviceDisplayName: String): String? {
        val serviceType = getServiceTypeByDisplayName(serviceDisplayName)
        val languageList = serviceType?.languageList ?: fullLanguageList
        return getLanguageCode(displayName, languageList)
    }

    // 验证语言是否在指定服务中可用
    fun isLanguageAvailable(displayName: String, serviceDisplayName: String): Boolean {
        val serviceType = getServiceTypeByDisplayName(serviceDisplayName)
        val languageList = serviceType?.languageList ?: fullLanguageList
        return languageList.any { it.show == displayName }
    }

    // 根据服务显示名称获取支持的语言列表
    fun getLanguageListForService(serviceDisplayName: String): List<LanguageItem> {
        val serviceType = getServiceTypeByDisplayName(serviceDisplayName)
        return serviceType?.languageList ?: fullLanguageList
    }

    // 根据服务显示名称获取服务类型标识
    fun getServiceTypeIdentifier(serviceDisplayName: String): String {
        val serviceType = getServiceTypeByDisplayName(serviceDisplayName)
        return serviceType?.type ?: AsrServiceType.ONLINE_1.type
    }

    /**
     * 创建语言列表，用于循环切换
     * @param primaryLanguageShow 主语言显示名称
     * @param secondaryLanguageShow 次语言显示名称
     * @param serviceDisplayName 服务显示名称
     * @return 包含主次语言的列表，第一个为当前选中
     */
    fun createLanguageList(
        primaryLanguageShow: String,
        secondaryLanguageShow: String,
        serviceDisplayName: String,
        currentIsPrimary: Boolean = true
    ): List<LanguageItem> {
        val serviceType = getServiceTypeByDisplayName(serviceDisplayName)
        val availableLanguages = serviceType?.languageList ?: fullLanguageList

        // 查找主次语言的完整信息
        val primaryLang = availableLanguages.find { it.show == primaryLanguageShow }
        val secondaryLang = availableLanguages.find { it.show == secondaryLanguageShow }

        if (primaryLang == null || secondaryLang == null) {
            // 如果找不到语言，返回默认列表
            return listOf(
                LanguageItem("English-United States", "en-US", currentIsPrimary, true),
                LanguageItem("Chinese-Mandarin", "zh-CN", !currentIsPrimary, false)
            )
        }

        return listOf(
            primaryLang.copy(isShow = currentIsPrimary, main = true),
            secondaryLang.copy(isShow = !currentIsPrimary, main = false)
        )
    }

    /**
     * 切换到下一个语言
     * @param langList 当前语言列表
     * @return 更新后的语言列表和当前选中的语言
     */
    fun switchToNextLanguage(langList: List<LanguageItem>): Pair<List<LanguageItem>, LanguageItem> {
        val currentIndex = langList.indexOfFirst { it.isShow }
        val nextIndex = (currentIndex + 1) % langList.size

        val updatedList = langList.mapIndexed { index, item ->
            item.copy(isShow = index == nextIndex)
        }

        return Pair(updatedList, updatedList[nextIndex])
    }

    /**
     * 获取当前选中的语言
     */
    fun getCurrentLanguage(langList: List<LanguageItem>): LanguageItem? {
        return langList.find { it.isShow }
    }

    /**
     * 获取目标语言（用于翻译）
     */
    fun getTargetLanguage(langList: List<LanguageItem>): LanguageItem? {
        return langList.find { !it.isShow }
    }
}
