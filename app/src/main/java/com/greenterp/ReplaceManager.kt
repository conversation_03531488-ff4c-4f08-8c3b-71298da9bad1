package com.greenterp

import android.content.Context
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.greenterp.network.ApiService
import com.greenterp.data.ReplaceManagementData
import com.greenterp.data.ReplaceManagementListResult
import com.greenterp.ProcessedText
import com.greenterp.TextHighlight

/**
 * 替换表管理器
 * 负责管理替换表数据的加载、存储和处理
 */
class ReplaceManager(private val context: Context) {
    
    companion object {
        private const val TAG = "ReplaceManager"
    }
    
    private val apiService = ApiService.getInstance()
    
    // 当前替换表数据
    private val _replaceItems = MutableStateFlow<List<ReplaceManagementData>>(emptyList())
    val replaceItems: StateFlow<List<ReplaceManagementData>> = _replaceItems.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    /**
     * 根据场景ID获取替换表
     */
    suspend fun loadReplacesBySceneId(sceneId: Int): Boolean {
        if (sceneId <= 0) {
            Log.w(TAG, "场景ID无效，清空替换表数据")
            _replaceItems.value = emptyList()
            _errorMessage.value = null
            return true
        }
        
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val userId = apiService.getCurrentUserId()
            if (userId.isEmpty()) {
                _errorMessage.value = "用户未登录"
                Log.e(TAG, "用户未登录，无法获取替换表")
                return false
            }

            Log.d(TAG, "请求替换表，场景ID: $sceneId, 用户ID: $userId")

            // 获取所有替换表数据（不分页）
            val result = apiService.getReplaceManagementList(0, 1000, sceneId, userId, null)

            when (result) {
                is ReplaceManagementListResult.Success -> {
                    val items = result.replaceList.data
                    _replaceItems.value = items
                    Log.d(TAG, "成功获取替换表，共 ${items.size} 条记录")

                    // 打印替换表内容用于调试
                    items.forEach { item ->
                        Log.d(TAG, "替换: ${item.replaceA} -> ${item.replaceB}")
                    }

                    true
                }
                is ReplaceManagementListResult.Error -> {
                    _errorMessage.value = result.message
                    Log.e(TAG, "获取替换表失败: ${result.message}")
                    false
                }
                is ReplaceManagementListResult.Loading -> {
                    false
                }
            }
        } catch (e: Exception) {
            val errorMsg = "获取替换表异常: ${e.message}"
            _errorMessage.value = errorMsg
            Log.e(TAG, errorMsg, e)
            false
        } finally {
            _isLoading.value = false
        }
    }
    
    /**
     * 清空替换表数据
     */
    fun clearReplace() {
        _replaceItems.value = emptyList()
        _errorMessage.value = null
        Log.d(TAG, "清空替换表数据")
    }
    
    /**
     * 清空错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 处理ASR识别文本，应用替换表规则并添加高亮标记
     * 参考JS算法，支持中文和英文的替换处理
     * @param text ASR识别的原始文本
     * @param languageCode 语言代码，用于判断是否为中文
     * @return 处理后的文本，包含替换和高亮信息
     */
    fun processAsrText(text: String, languageCode: String = ""): ProcessedText {
        val items = _replaceItems.value
        if (items.isEmpty() || text.isBlank()) {
            return ProcessedText(text, emptyList())
        }

        var processedText = text
        val highlights = mutableListOf<TextHighlight>()
        var offsetAdjustment = 0 // 用于跟踪由于替换导致的位置偏移

        // 按替换词长度降序排序，优先处理长词，避免短词覆盖长词
        val sortedItems = items.sortedByDescending { it.replaceA.length }

        for (item in sortedItems) {
            val replaceA = item.replaceA
            val replaceB = item.replaceB

            // 判断是否为中文语言
            val isChinese = languageCode in listOf("zh-CN", "zh-HK", "zh-TW", "wuu-CN", "yue-CN")

            val pattern = if (isChinese) {
                // 中文处理：直接使用全局替换
                replaceA.toRegex(RegexOption.IGNORE_CASE)
            } else {
                // 英文处理：使用单词边界匹配
                "\\b${Regex.escape(replaceA)}\\b".toRegex(RegexOption.IGNORE_CASE)
            }

            // 查找所有匹配项
            val matches = pattern.findAll(processedText).toList()

            if (matches.isNotEmpty()) {
                // 从后往前替换，避免索引偏移问题
                for (match in matches.reversed()) {
                    val start = match.range.first
                    val end = match.range.last + 1

                    // 执行替换
                    processedText = processedText.replaceRange(start, end, replaceB)

                    // 记录高亮位置（基于替换后的文本位置）
                    val highlightStart = start
                    val highlightEnd = start + replaceB.length
                    highlights.add(TextHighlight(
                        start = highlightStart,
                        end = highlightEnd,
                        originalWord = replaceA,
                        translatedWord = replaceB,
                        isGlossaryA = false // 替换表不是词汇表，设为false
                    ))

                    Log.d(TAG, "替换: '$replaceA' -> '$replaceB' at position $start-$end, 高亮位置: $highlightStart-$highlightEnd")
                }
            }
        }

        // 按位置排序高亮区域
        highlights.sortBy { it.start }

        Log.d(TAG, "替换处理完成: 原文='$text' -> 处理后='$processedText', 高亮数量=${highlights.size}")

        return ProcessedText(processedText, highlights)
    }
    
    /**
     * 获取所有原文词汇（用于ASR识别时的匹配）
     */
    fun getAllOriginalTexts(): List<String> {
        return _replaceItems.value.map { it.replaceA }
    }
    
    /**
     * 获取当前替换表数量
     */
    fun getReplaceCount(): Int {
        return _replaceItems.value.size
    }
    
    /**
     * 根据原文查找替换文
     */
    fun findReplacement(originalText: String): String? {
        val items = _replaceItems.value
        return items.find { it.replaceA.equals(originalText, ignoreCase = true) }?.replaceB
    }
}
