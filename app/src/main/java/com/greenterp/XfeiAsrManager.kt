package com.greenterp

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.core.content.ContextCompat
import com.greenterp.data.RecognitionResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 讯飞语音识别管理器
 * 管理语音识别的状态和结果
 */
class XfeiAsrManager(private val context: Context) {
    
    companion object {
        private const val TAG = "XfeiAsrManager"
    }
    
    private val asrService = XfeiAsrService()
    
    // 识别状态
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()
    
    // 当前会话ID
    private val _currentSessionId = MutableStateFlow<String?>(null)
    val currentSessionId: StateFlow<String?> = _currentSessionId.asStateFlow()
    
    // 识别结果
    private val _recognitionResults = MutableStateFlow<List<RecognitionResult>>(emptyList())
    val recognitionResults: StateFlow<List<RecognitionResult>> = _recognitionResults.asStateFlow()

    // 当前结果索引计数器
    private var currentResultIndex = 0
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 连接状态
    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    // 当前语言代码
    private var currentLanguageCode: String = "en-US"
    
    private val asrCallback = object : XfeiAsrService.AsrCallback {
        override fun onConnected(sessionId: String) {
            Log.d(TAG, "ASR连接成功: $sessionId")
            _currentSessionId.value = sessionId
            _isConnected.value = true
            _isRecording.value = true  // 在真正连接成功时才设置录音状态
            _errorMessage.value = null
        }
        
        override fun onResult(text: String, isIntermediate: Boolean) {
            Log.d(TAG, "ASR识别结果: $text, isIntermediate: $isIntermediate")

            val currentResults = _recognitionResults.value.toMutableList()

            if (isIntermediate) {
                // 识别中状态 - 更新或添加中间结果
                val intermediateResult = RecognitionResult(
                    transcript = text,
                    isIntermediate = true,
                    index = currentResultIndex
                )

                // 查找是否已有中间结果
                val existingIndex = currentResults.indexOfFirst { it.isIntermediate && it.index == currentResultIndex }
                if (existingIndex >= 0) {
                    currentResults[existingIndex] = intermediateResult
                } else {
                    currentResults.add(intermediateResult)
                }
            } else {
                // 识别完成状态 - 添加最终结果，移除对应的中间结果
                val finalResult = RecognitionResult(
                    transcript = text,
                    isIntermediate = false,
                    index = currentResultIndex++
                )

                // 移除对应的中间结果
                currentResults.removeAll { it.isIntermediate && it.index == finalResult.index }
                // 添加最终结果
                currentResults.add(finalResult)
            }

            _recognitionResults.value = currentResults
        }
        
        override fun onError(code: String, message: String) {
            Log.e(TAG, "ASR错误: $code - $message")
            _errorMessage.value = "$code: $message"
            _isRecording.value = false
            _isConnected.value = false
        }
        
        override fun onDisconnected() {
            Log.d(TAG, "ASR连接断开")
            _isConnected.value = false
            _isRecording.value = false
            _currentSessionId.value = null
        }
    }
    
    /**
     * 检查录音权限
     */
    fun hasRecordPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 设置语言
     */
    fun setLanguage(languageCode: String) {
        this.currentLanguageCode = languageCode
        Log.d(TAG, "设置讯飞ASR语言: $languageCode")
    }
    
    /**
     * 开始语音识别
     */
    fun startRecognition(): Boolean {
        if (!hasRecordPermission()) {
            _errorMessage.value = "Recording permission required"
            return false
        }

        if (_isRecording.value) {
            Log.w(TAG, "已经在录音中")
            return false
        }

        // 清空之前的结果
        _recognitionResults.value = emptyList()
        _errorMessage.value = null
        // 注意：不要在这里设置 _isRecording.value = true
        // 等待讯飞服务真正连接成功后再设置

        // 重置结果索引计数器
        currentResultIndex = 0

        // 开始识别
        asrService.startRecognition(asrCallback)

        return true
    }
    
    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        if (!_isRecording.value) {
            Log.w(TAG, "没有在录音")
            return
        }
        
        _isRecording.value = false
        asrService.stopRecognition()
    }
    
    /**
     * 清空错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 清空识别结果
     */
    fun clearResults() {
        _recognitionResults.value = emptyList()
    }
    
    /**
     * 获取当前识别结果的文本
     */
    fun getCurrentResultText(): String {
        return _recognitionResults.value.joinToString("")
    }
}
