package com.greenterp.network

import android.util.Log
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import java.io.File
import java.util.concurrent.TimeUnit
import java.util.UUID
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import com.greenterp.data.GlossaryRequest
import com.greenterp.data.GlossaryResponse

/**
 * API服务类
 */
class ApiService {
    companion object {
        private const val TAG = "ApiService"

        @Volatile
        private var INSTANCE: ApiService? = null

        fun getInstance(): ApiService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ApiService().also { INSTANCE = it }
            }
        }
    }

    // 全局变量，用于控制是否使用GT
    private var useGT = false

    private val gson = Gson()
    private var userManager: com.greenterp.data.UserManager? = null

    /**
     * 初始化UserManager
     */
    fun initUserManager(context: android.content.Context) {
        userManager = com.greenterp.data.UserManager.getInstance(context)
    }

    /**
     * 获取当前用户ID
     */
    fun getCurrentUserId(): String {
        return userManager?.getUserId() ?: "" // 如果没有登录信息，使用默认值
    }

    /**
     * 获取当前用户信息
     */
    fun getCurrentUserInfo(): UserData? {
        return userManager?.getUserData()
    }
    
    // OkHttp客户端配置
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(ApiConfig.CONNECT_TIMEOUT, TimeUnit.SECONDS)
        .readTimeout(ApiConfig.READ_TIMEOUT, TimeUnit.SECONDS)
        .writeTimeout(ApiConfig.WRITE_TIMEOUT, TimeUnit.SECONDS)
        .addInterceptor(HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        })
        .build()
    
    /**
     * 登录接口
     */
    suspend fun login(emailAddress: String, password: String): LoginResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting login request: $emailAddress")
                
                val loginRequest = LoginRequest(emailAddress, password)
                val requestBody = gson.toJson(loginRequest)
                    .toRequestBody("application/json".toMediaType())
                
                val request = Request.Builder()
                    .url(ApiConfig.BASE_URL + ApiConfig.Endpoints.LOGIN)
                    .post(requestBody)
                    .build()
                
                val response = httpClient.newCall(request).execute()
                
                response.use {
                    val responseBody = it.body?.string()
                    Log.d(TAG, "Login response: ${it.code} - $responseBody")

                    if (it.isSuccessful && responseBody != null) {
                        try {
                            val loginResponse = gson.fromJson(responseBody, LoginResponse::class.java)
                            if (loginResponse.success && loginResponse.data != null) {
                                // 先清除之前的用户数据，确保不会有数据混淆
                                userManager?.clearUserData()

                                // 保存新用户信息
                                Log.d(TAG, "登录成功，保存用户数据: ${loginResponse.data.emailAddress}")
                                userManager?.saveUserData(loginResponse.data)
                            } else {
                                Log.d(TAG, "登录失败或无用户数据: success=${loginResponse.success}, data=${loginResponse.data}")
                            }
                            LoginResult.Success(loginResponse)
                        } catch (e: Exception) {
                            Log.e(TAG, "Failed to parse login response", e)
                            LoginResult.Error("Response parsing failed: ${e.message}")
                        }
                    } else {
                        LoginResult.Error("Network request failed: ${it.code} ${it.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Login request exception", e)
                LoginResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 获取翻译模型列表接口
     */
    suspend fun getTranslationModelList(): TranslationModelListResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting translation model list request")

                val request = Request.Builder()
                    .url(ApiConfig.BASE_URL + ApiConfig.Endpoints.TRANSLATION_MODEL_LIST)
                    .get()
                    .build()

                val response = httpClient.newCall(request).execute()

                response.use {
                    val responseBody = it.body?.string()
                    Log.d(TAG, "Translation model list response: ${it.code} - $responseBody")

                    if (it.isSuccessful && responseBody != null) {
                        try {
                            val modelListResponse = gson.fromJson(responseBody, TranslationModelListResponse::class.java)
                            TranslationModelListResult.Success(modelListResponse)
                        } catch (e: Exception) {
                            Log.e(TAG, "Failed to parse translation model list response", e)
                            TranslationModelListResult.Error("Response parsing failed: ${e.message}")
                        }
                    } else {
                        TranslationModelListResult.Error("Network request failed: ${it.code} ${it.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Translation model list request exception", e)
                TranslationModelListResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 获取词汇表列表接口
     */
    suspend fun getVocabularyList(startPage: Int, pageNum: Int, userId: String, searchInfo: String? = null): com.greenterp.data.VocabularyListResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary list request: startPage=$startPage, pageNum=$pageNum, userId=$userId, searchInfo=$searchInfo")

                val vocabularyRequest = com.greenterp.data.VocabularyListRequest(startPage, pageNum, userId, searchInfo)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}/conf/asr/glossaryScene/list")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary list response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularyListResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularyListResult.Success(vocabularyResponse.data)
                    } else {
                        com.greenterp.data.VocabularyListResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularyListResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary list request exception", e)
                com.greenterp.data.VocabularyListResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 保存词汇表接口
     */
    suspend fun saveVocabulary(name: String, userId: String): com.greenterp.data.VocabularySaveResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary save request: name=$name, userId=$userId")

                val vocabularyRequest = com.greenterp.data.VocabularySaveRequest(name, userId)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}/conf/asr/glossaryScene/save")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary save response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularySaveResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularySaveResult.Success(vocabularyResponse.data)
                    } else {
                        com.greenterp.data.VocabularySaveResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularySaveResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary save request exception", e)
                com.greenterp.data.VocabularySaveResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 更新词汇表接口
     */
    suspend fun updateVocabulary(id: Int, name: String, userId: String): com.greenterp.data.VocabularySaveResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary update request: id=$id, name=$name, userId=$userId")

                val vocabularyRequest = com.greenterp.data.VocabularyUpdateRequest(id, name, userId)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}/conf/asr/glossaryScene/save")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary update response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularySaveResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularySaveResult.Success(vocabularyResponse.data)
                    } else {
                        com.greenterp.data.VocabularySaveResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularySaveResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary update request exception", e)
                com.greenterp.data.VocabularySaveResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 获取所有词汇表接口
     */
    suspend fun getAllVocabularyList(userId: String): com.greenterp.data.VocabularyListAllResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary list all request: userId=$userId")

                val vocabularyRequest = com.greenterp.data.VocabularyListAllRequest(userId)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}/conf/asr/glossaryScene/listAll")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary list all response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularyListAllResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularyListAllResult.Success(vocabularyResponse.data)
                    } else {
                        com.greenterp.data.VocabularyListAllResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularyListAllResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary list all request exception", e)
                com.greenterp.data.VocabularyListAllResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 删除词汇表接口
     */
    suspend fun deleteVocabulary(id: Int): com.greenterp.data.VocabularyDeleteResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary delete request: id=$id")

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/glossaryScene/deleteById/$id")
                    .get()
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary delete response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularyDeleteResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularyDeleteResult.Success
                    } else {
                        com.greenterp.data.VocabularyDeleteResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularyDeleteResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary delete request exception", e)
                com.greenterp.data.VocabularyDeleteResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 获取词汇管理列表接口
     */
    suspend fun getVocabularyManagementList(startPage: Int, pageNum: Int, sceneId: Int, userId: String, searchInfo: String? = null): com.greenterp.data.VocabularyManagementListResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary management list request: startPage=$startPage, pageNum=$pageNum, sceneId=$sceneId, userId=$userId, searchInfo=$searchInfo")

                val vocabularyRequest = com.greenterp.data.VocabularyManagementListRequest(startPage, pageNum, sceneId, userId, searchInfo)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/glossary/list")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary management list response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularyManagementListResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularyManagementListResult.Success(vocabularyResponse.data)
                    } else {
                        com.greenterp.data.VocabularyManagementListResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularyManagementListResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary management list request exception", e)
                com.greenterp.data.VocabularyManagementListResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 保存词汇管理接口
     */
    suspend fun saveVocabularyManagement(glossaryA: String, glossaryB: String, sceneId: Int, userId: String, id: Int? = null): com.greenterp.data.VocabularyManagementSaveResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary management save request: glossaryA=$glossaryA, glossaryB=$glossaryB, sceneId=$sceneId, userId=$userId, id=$id")

                val vocabularyRequest = com.greenterp.data.VocabularyManagementSaveRequest(glossaryA, glossaryB, sceneId, userId, id)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/glossary/save")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary management save response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularyManagementSaveResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularyManagementSaveResult.Success
                    } else {
                        com.greenterp.data.VocabularyManagementSaveResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularyManagementSaveResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary management save request exception", e)
                com.greenterp.data.VocabularyManagementSaveResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 删除词汇管理接口
     */
    suspend fun deleteVocabularyManagement(ids: List<Int>, userId: String): com.greenterp.data.VocabularyManagementDeleteResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary management delete request: ids=$ids, userId=$userId")

                val vocabularyRequest = com.greenterp.data.VocabularyManagementDeleteRequest(ids, userId)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/glossary/delete")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary management delete response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularyManagementDeleteResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularyManagementDeleteResult.Success
                    } else {
                        com.greenterp.data.VocabularyManagementDeleteResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularyManagementDeleteResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary management delete request exception", e)
                com.greenterp.data.VocabularyManagementDeleteResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 导出词汇管理接口
     */
    suspend fun exportVocabularyManagement(sceneId: Int, userId: String): com.greenterp.data.VocabularyManagementExportResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary management export request: sceneId=$sceneId, userId=$userId")

                val vocabularyRequest = com.greenterp.data.VocabularyManagementExportRequest(sceneId, userId)
                val requestBody = gson.toJson(vocabularyRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/glossary/export")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()

                Log.d(TAG, "Vocabulary management export response code: ${response.code}")

                if (response.isSuccessful) {
                    val responseBody = response.body
                    if (responseBody != null) {
                        val fileBytes = responseBody.bytes()
                        Log.d(TAG, "Vocabulary management export file size: ${fileBytes.size} bytes")
                        com.greenterp.data.VocabularyManagementExportResult.Success(fileBytes)
                    } else {
                        com.greenterp.data.VocabularyManagementExportResult.Error("Empty response body")
                    }
                } else {
                    com.greenterp.data.VocabularyManagementExportResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary management export request exception", e)
                com.greenterp.data.VocabularyManagementExportResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 导入词汇管理接口
     */
    suspend fun importVocabularyManagement(file: File, sceneId: Int, userId: String): com.greenterp.data.VocabularyManagementImportResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting vocabulary management import request: file=${file.name}, sceneId=$sceneId, userId=$userId")

                // 创建multipart请求体
                val fileRequestBody = file.asRequestBody("application/vnd.ms-excel".toMediaType())
                val multipartBody = MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", file.name, fileRequestBody)
                    .build()

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/glossary/import?userId=$userId&sceneId=$sceneId")
                    .post(multipartBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Vocabulary management import response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val vocabularyResponse = gson.fromJson(responseBody, com.greenterp.data.VocabularyManagementImportResponse::class.java)
                    if (vocabularyResponse.success) {
                        com.greenterp.data.VocabularyManagementImportResult.Success
                    } else {
                        com.greenterp.data.VocabularyManagementImportResult.Error(vocabularyResponse.msg)
                    }
                } else {
                    com.greenterp.data.VocabularyManagementImportResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Vocabulary management import request exception", e)
                com.greenterp.data.VocabularyManagementImportResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 翻译搜索主入口方法
     */
    suspend fun translateSearch(data: TranslateSearchRequest): TranslateSearchResult {
        val translateModel = data.translateApi
        return when (translateModel.type) {
            2 -> microsoftTranslateSearch(data)
            3 -> googleTranslateSearch(data)
            4 -> googleTranslateSearchByChromeExtension(data)
            else -> microsoftTranslateSearch(data)
        }
    }

    /**
     * 微软翻译
     */
    suspend fun microsoftTranslateSearch(req: TranslateSearchRequest): TranslateSearchResult {
        return withContext(Dispatchers.IO) {
            try {
                var translateModel = req.translateApi
                // 检查微软翻译密钥是否存在
                var microsoftKey = translateModel.microsoftKey
                if (microsoftKey.isNullOrEmpty()) {
                    return@withContext TranslateSearchResult.Error("Microsoft translate key is not configured")
                }
                // 如果使用GT，则使用备用key
                if (useGT) {
                    //microsoftKey = "f2cb3ca133364ba18781f24a15fc9c8d"
                    microsoftKey = "3ppwpHjYgB3Wja2mwJar7fUKp73PdYRri7vuIJOXmGFC716rzm7nJQQJ99BGAC1i4TkXJ3w3AAAbACOG1nTl"
                    translateModel = translateModel.copy(microsoftKey = microsoftKey)
                }

                val requestUrl = "https://api.cognitive.microsofttranslator.com/translate"

                // 构建URL参数
                val urlBuilder = requestUrl.toHttpUrlOrNull()?.newBuilder()
                urlBuilder?.addQueryParameter("api-version", "3.0")
                urlBuilder?.addQueryParameter("from", req.source_lang.split("-")[0])
                urlBuilder?.addQueryParameter("to", req.target_lang.split("-")[0])

                val finalUrl = urlBuilder?.build()?.toString() ?: requestUrl
                Log.d(TAG, "Microsoft translate microsoftKey: ${microsoftKey}")
                // 构建请求头
                val requestBuilder = Request.Builder()
                    .url(finalUrl)
                    .addHeader("Ocp-Apim-Subscription-Key", microsoftKey)
                    .addHeader("Content-type", "application/json")
                    .addHeader("X-ClientTraceId", UUID.randomUUID().toString())

                // 如果是特定key，添加region头
//                if (useGT) {
                requestBuilder.addHeader("Ocp-Apim-Subscription-Region", "centralus")
//                }
                Log.d(TAG, "Microsoft translate microsoftKey: ${microsoftKey} ")
                // 构建请求体
                val requestBodyList = listOf(MicrosoftTranslateRequestBody(req.text))
                val requestBody = gson.toJson(requestBodyList)
                    .toRequestBody("application/json".toMediaType())

                val request = requestBuilder.post(requestBody).build()

                var resp: List<MicrosoftTranslateResponse>? = null
                try {
                    val response = httpClient.newCall(request).execute()
                    response.use {
                        val responseBody = it.body?.string()
                        Log.d(TAG, "Microsoft translate response: ${it.code} - $responseBody")

                        if (it.isSuccessful && responseBody != null) {
                            resp = gson.fromJson(responseBody, Array<MicrosoftTranslateResponse>::class.java).toList()
                        } else {
                            throw Exception("Request failed: ${it.code} ${it.message}")
                        }
                    }
                } catch (error: Exception) {
                    Log.e(TAG, "Microsoft translate error", error)
                    // 如果当前是cymo key，切换成gt
                    if (translateModel.microsoftKey == "f2cb3ca133364ba18781f24a15fc9c8d") {
                        useGT = true
                    }
                    throw error
                }

                // 返回结果
                val translatedText = resp?.get(0)?.translations?.get(0)?.text ?: ""
                TranslateSearchResult.Success(
                    TranslateSearchResponse(
                        code = 200,
                        data = translatedText
                    )
                )

            } catch (e: Exception) {
                Log.e(TAG, "Microsoft translate search exception", e)
                TranslateSearchResult.Error("Microsoft translate error: ${e.message}")
            }
        }
    }

    /**
     * Google翻译
     * https://clients5.google.com/translate_a/t?client=dict-chrome-ex&sl=auto&tl=zh-CN&q=hello
     */
    suspend fun googleTranslateSearch(req: TranslateSearchRequest): TranslateSearchResult {
        return withContext(Dispatchers.IO) {
            try {
                val translateModel = req.translateApi
                val requestUrl = translateModel.googleNokeyUrl

                // 检查Google翻译URL是否存在
                if (requestUrl.isNullOrEmpty()) {
                    return@withContext TranslateSearchResult.Error("Google translate URL is not configured")
                }

                // 构建URL参数
                val urlBuilder = requestUrl.toHttpUrlOrNull()?.newBuilder()
                urlBuilder?.addQueryParameter("client", "dict-chrome-ex")
                urlBuilder?.addQueryParameter("sl", "auto")
                urlBuilder?.addQueryParameter("tl", req.target_lang)
                urlBuilder?.addQueryParameter("q", req.text)

                val finalUrl = urlBuilder?.build()?.toString() ?: requestUrl
                Log.d(TAG, "Google translate requestUrl: $finalUrl")

                val request = Request.Builder()
                    .url(finalUrl)
                    .get()
                    .build()

                val response = httpClient.newCall(request).execute()

                response.use {
                    val responseBody = it.body?.string()
                    Log.d(TAG, "Google translate response: ${it.code} - $responseBody")

                    if (it.isSuccessful && responseBody != null) {
                        // 解析Google翻译响应
                        // 响应格式: [["你好", "en"]]
                        val resp = gson.fromJson(responseBody, Array<Array<String>>::class.java)
                        val translatedText = resp[0][0] // 第一个数组的第一个元素是翻译结果

                        TranslateSearchResult.Success(
                            TranslateSearchResponse(
                                code = 200,
                                data = translatedText
                            )
                        )
                    } else {
                        TranslateSearchResult.Error("Google translate request failed: ${it.code} ${it.message}")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Google translate search exception", e)
                TranslateSearchResult.Error("Google translate error: ${e.message}")
            }
        }
    }

    /**
     * Google Chrome扩展翻译
     */
    suspend fun googleTranslateSearchByChromeExtension(req: TranslateSearchRequest): TranslateSearchResult {
        return withContext(Dispatchers.IO) {
            try {
                val translateModel = req.translateApi
                val requestUrl = translateModel.googleExtensionUrl
                val extensionKey = translateModel.googleExtensionKey
                val xRefererKey = translateModel.googleXREFKey

                // 检查必要的配置是否存在
                if (requestUrl.isNullOrEmpty()) {
                    return@withContext TranslateSearchResult.Error("Google Chrome extension URL is not configured")
                }
                if (extensionKey.isNullOrEmpty()) {
                    return@withContext TranslateSearchResult.Error("Google Chrome extension key is not configured")
                }
                if (xRefererKey.isNullOrEmpty()) {
                    return@withContext TranslateSearchResult.Error("Google Chrome extension x-referer key is not configured")
                }

                // 构建URL参数
                val urlBuilder = requestUrl.toHttpUrlOrNull()?.newBuilder()
                urlBuilder?.addQueryParameter("language", req.target_lang)
                urlBuilder?.addQueryParameter("key", extensionKey)
                urlBuilder?.addQueryParameter("strategy", "2")
                urlBuilder?.addQueryParameter("term", req.text)

                val finalUrl = urlBuilder?.build()?.toString() ?: requestUrl
                Log.d(TAG, "Google Chrome extension requestUrl: $finalUrl")

                val request = Request.Builder()
                    .url(finalUrl)
                    .addHeader("x-referer", xRefererKey)
                    .get()
                    .build()

                val response = httpClient.newCall(request).execute()

                response.use {
                    val responseBody = it.body?.string()
                    Log.d(TAG, "Google Chrome extension response: ${it.code} - $responseBody")

                    if (it.isSuccessful && responseBody != null) {
                        // 解析Google Chrome扩展翻译响应
                        val resp = gson.fromJson(responseBody, GoogleChromeExtensionResponse::class.java)
                        val translatedText = resp.translateResponse.translateText

                        TranslateSearchResult.Success(
                            TranslateSearchResponse(
                                code = 200,
                                data = translatedText
                            )
                        )
                    } else {
                        TranslateSearchResult.Error("Google Chrome extension translate request failed: ${it.code} ${it.message}")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Google Chrome extension translate search exception", e)
                TranslateSearchResult.Error("Google Chrome extension translate error: ${e.message}")
            }
        }
    }

    // ==================== 替换表相关API ====================

    /**
     * 获取替换表列表接口
     */
    suspend fun getReplaceSceneList(startPage: Int, pageNum: Int, userId: String, searchInfo: String? = null): com.greenterp.data.ReplaceSceneListResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace scene list request: startPage=$startPage, pageNum=$pageNum, userId=$userId, searchInfo=$searchInfo")

                val replaceSceneRequest = com.greenterp.data.ReplaceSceneListRequest(startPage, pageNum, userId, searchInfo)
                val requestBody = gson.toJson(replaceSceneRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaceScene/list")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace scene list response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val replaceSceneResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceSceneListResponse::class.java)
                    if (replaceSceneResponse.success) {
                        com.greenterp.data.ReplaceSceneListResult.Success(replaceSceneResponse.data)
                    } else {
                        com.greenterp.data.ReplaceSceneListResult.Error(replaceSceneResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceSceneListResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace scene list request exception", e)
                com.greenterp.data.ReplaceSceneListResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 保存替换表接口
     */
    suspend fun saveReplaceScene(name: String, userId: String, id: Int? = null): com.greenterp.data.ReplaceSceneSaveResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace scene save request: name=$name, userId=$userId, id=$id")

                val replaceSceneRequest = com.greenterp.data.ReplaceSceneSaveRequest(name, userId, id)
                val requestBody = gson.toJson(replaceSceneRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaceScene/save")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace scene save response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val replaceSceneResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceSceneSaveResponse::class.java)
                    if (replaceSceneResponse.success) {
                        com.greenterp.data.ReplaceSceneSaveResult.Success(replaceSceneResponse.data)
                    } else {
                        com.greenterp.data.ReplaceSceneSaveResult.Error(replaceSceneResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceSceneSaveResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace scene save request exception", e)
                com.greenterp.data.ReplaceSceneSaveResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 删除替换表接口
     */
    suspend fun deleteReplaceScene(id: Int): com.greenterp.data.ReplaceSceneDeleteResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace scene delete request: id=$id")

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaceScene/deleteById/$id")
                    .get()
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace scene delete response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val deleteResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceSceneDeleteResponse::class.java)
                    if (deleteResponse.success) {
                        com.greenterp.data.ReplaceSceneDeleteResult.Success("Delete successful")
                    } else {
                        com.greenterp.data.ReplaceSceneDeleteResult.Error(deleteResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceSceneDeleteResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace scene delete request exception", e)
                com.greenterp.data.ReplaceSceneDeleteResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 获取所有替换表接口
     */
    suspend fun getAllReplaceSceneList(userId: String): com.greenterp.data.ReplaceSceneListAllResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace scene list all request: userId=$userId")

                val replaceSceneRequest = com.greenterp.data.ReplaceSceneListAllRequest(userId)
                val requestBody = gson.toJson(replaceSceneRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaceScene/listAll")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace scene list all response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val replaceSceneResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceSceneListAllResponse::class.java)
                    if (replaceSceneResponse.success) {
                        com.greenterp.data.ReplaceSceneListAllResult.Success(replaceSceneResponse.data)
                    } else {
                        com.greenterp.data.ReplaceSceneListAllResult.Error(replaceSceneResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceSceneListAllResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace scene list all request exception", e)
                com.greenterp.data.ReplaceSceneListAllResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 获取替换表管理列表接口
     */
    suspend fun getReplaceManagementList(startPage: Int, pageNum: Int, sceneId: Int, userId: String, searchInfo: String? = null): com.greenterp.data.ReplaceManagementListResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace management list request: startPage=$startPage, pageNum=$pageNum, sceneId=$sceneId, userId=$userId, searchInfo=$searchInfo")

                val replaceRequest = com.greenterp.data.ReplaceManagementListRequest(startPage, pageNum, sceneId, userId, searchInfo)
                val requestBody = gson.toJson(replaceRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaces/list")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace management list response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val replaceResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceManagementListResponse::class.java)
                    if (replaceResponse.success) {
                        com.greenterp.data.ReplaceManagementListResult.Success(replaceResponse.data)
                    } else {
                        com.greenterp.data.ReplaceManagementListResult.Error(replaceResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceManagementListResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace management list request exception", e)
                com.greenterp.data.ReplaceManagementListResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 保存替换表管理接口
     */
    suspend fun saveReplaceManagement(replaceA: String, replaceB: String, sceneId: Int, userId: String, id: Int? = null): com.greenterp.data.ReplaceManagementSaveResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace management save request: replaceA=$replaceA, replaceB=$replaceB, sceneId=$sceneId, userId=$userId, id=$id")

                val replaceRequest = com.greenterp.data.ReplaceManagementSaveRequest(replaceA, replaceB, sceneId, userId, id)
                val requestBody = gson.toJson(replaceRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaces/save")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace management save response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val replaceResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceManagementSaveResponse::class.java)
                    if (replaceResponse.success) {
                        com.greenterp.data.ReplaceManagementSaveResult.Success
                    } else {
                        com.greenterp.data.ReplaceManagementSaveResult.Error(replaceResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceManagementSaveResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace management save request exception", e)
                com.greenterp.data.ReplaceManagementSaveResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 删除替换表管理接口
     */
    suspend fun deleteReplaceManagement(ids: List<Int>, userId: String): com.greenterp.data.ReplaceManagementDeleteResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace management delete request: ids=$ids, userId=$userId")

                val replaceRequest = com.greenterp.data.ReplaceManagementDeleteRequest(ids, userId)
                val requestBody = gson.toJson(replaceRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaces/delete")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace management delete response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val replaceResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceManagementDeleteResponse::class.java)
                    if (replaceResponse.success) {
                        com.greenterp.data.ReplaceManagementDeleteResult.Success
                    } else {
                        com.greenterp.data.ReplaceManagementDeleteResult.Error(replaceResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceManagementDeleteResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace management delete request exception", e)
                com.greenterp.data.ReplaceManagementDeleteResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 导出替换表管理接口
     */
    suspend fun exportReplaceManagement(sceneId: Int, userId: String): com.greenterp.data.ReplaceManagementExportResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace management export request: sceneId=$sceneId, userId=$userId")

                val replaceRequest = com.greenterp.data.ReplaceManagementExportRequest(sceneId, userId)
                val requestBody = gson.toJson(replaceRequest)
                    .toRequestBody("application/json; charset=utf-8".toMediaType())

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaces/export")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()

                Log.d(TAG, "Replace management export response: ${response.code}")

                if (response.isSuccessful) {
                    val fileBytes = response.body?.bytes()
                    if (fileBytes != null) {
                        com.greenterp.data.ReplaceManagementExportResult.Success(fileBytes)
                    } else {
                        com.greenterp.data.ReplaceManagementExportResult.Error("Empty response body")
                    }
                } else {
                    com.greenterp.data.ReplaceManagementExportResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace management export request exception", e)
                com.greenterp.data.ReplaceManagementExportResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 导入替换表管理接口
     */
    suspend fun importReplaceManagement(fileBytes: ByteArray, fileName: String, userId: String, sceneId: Int): com.greenterp.data.ReplaceManagementImportResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting replace management import request: fileName=$fileName, userId=$userId, sceneId=$sceneId")

                val requestBody = MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart(
                        "file",
                        fileName,
                        fileBytes.toRequestBody("application/vnd.ms-excel".toMediaType())
                    )
                    .build()

                val request = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/replaces/import?userId=$userId&sceneId=$sceneId")
                    .post(requestBody)
                    .build()

                val response = httpClient.newCall(request).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Replace management import response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val replaceResponse = gson.fromJson(responseBody, com.greenterp.data.ReplaceManagementImportResponse::class.java)
                    if (replaceResponse.success) {
                        com.greenterp.data.ReplaceManagementImportResult.Success
                    } else {
                        com.greenterp.data.ReplaceManagementImportResult.Error(replaceResponse.msg)
                    }
                } else {
                    com.greenterp.data.ReplaceManagementImportResult.Error("Request failed: ${response.code}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Replace management import request exception", e)
                com.greenterp.data.ReplaceManagementImportResult.Error("Network error: ${e.message}")
            }
        }
    }

    /**
     * 根据场景ID获取词汇表
     */
    suspend fun getGlossaryBySceneIds(sceneIds: List<Int>, userId: String): GlossaryResponse? {
        return withContext(Dispatchers.IO) {
            try {
                val request = GlossaryRequest(
                    startPage = 0,
                    pageNum = 9999999,
                    searchInfo = "",
                    ids = sceneIds,
                    userId = userId
                )

                val requestBody = gson.toJson(request).toRequestBody("application/json".toMediaType())
                val httpRequest = Request.Builder()
                    .url("${ApiConfig.BASE_URL}conf/asr/glossary/listBySceneIds")
                    .post(requestBody)
                    .build()

                Log.d(TAG, "Glossary request: ${gson.toJson(request)}")

                val response = httpClient.newCall(httpRequest).execute()
                val responseBody = response.body?.string()

                Log.d(TAG, "Glossary response: $responseBody")

                if (response.isSuccessful && responseBody != null) {
                    val glossaryResponse = gson.fromJson(responseBody, GlossaryResponse::class.java)
                    if (glossaryResponse.success) {
                        Log.d(TAG, "成功获取词汇表，共 ${glossaryResponse.data.data.size} 条记录")
                        glossaryResponse
                    } else {
                        Log.e(TAG, "获取词汇表失败: ${glossaryResponse.msg}")
                        null
                    }
                } else {
                    Log.e(TAG, "词汇表请求失败: ${response.code}")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "词汇表请求异常", e)
                null
            }
        }
    }
}
