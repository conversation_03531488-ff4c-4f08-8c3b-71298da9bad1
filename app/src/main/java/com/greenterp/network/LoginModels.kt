package com.greenterp.network

/**
 * 登录请求数据类
 */
data class LoginRequest(
    val emailAddress: String,
    val password: String
)

/**
 * 用户数据类
 */
data class UserData(
    val id: Int,
    val createAt: String,
    val updateAt: String?,
    val userName: String,
    val emailAddress: String,
    val password: String,
    val role: Int,
    val balance: Double,
    val hourBalance: Double,
    val packageId: Int,
    val packageStartTime: String?,
    val packageEndTime: String?,
    val inviteCode: String,
    val parentInviteCode: String?,
    val inviteCount: Int,
    val userId: String,
    val isTopup: Int,
    val subscribe: Int,
    val subscribeId: String?,
    val technicianSubscribe: Int,
    val technicianSubscribeId: String?,
    val inBlacklist: Int,
    val authCode: Int,
    val business: Int,
    val interpreter: Int,
    val languageA: String?,
    val languageB: String?,
    val languageC: String?,
    val company: String?,
    val companyAddress: String?,
    val companyContactNumber: String?,
    val endUpdateInfo: Int,
    val oldPwd: String?,
    val comboPackage: String?,
    val comboPackageBak: String?,
    val technicianStartTime: String?,
    val technicianEndTime: String?,
    val packagePrice: Double,
    val monthCount: Int,
    val eula: Boolean,
    val checkUserExist: Int,
    val type: Int,
    val terpMetaTrialStartTime: String?
)

/**
 * 登录响应数据类
 */
data class LoginResponse(
    val code: Int,
    val time: Long,
    val msg: String,
    val success: Boolean,
    val data: UserData?
)

/**
 * 登录结果封装类
 */
sealed class LoginResult {
    object Loading : LoginResult()
    data class Success(val response: LoginResponse) : LoginResult()
    data class Error(val message: String) : LoginResult()
}

/**
 * 翻译请求数据类
 */
data class TranslationRequest(
    val text: String,
    val fromLanguage: String,
    val toLanguage: String
)

/**
 * 翻译响应数据类
 */
data class TranslationResponse(
    val success: Boolean,
    val message: String? = null,
    val data: TranslationData? = null
)

/**
 * 翻译数据
 */
data class TranslationData(
    val originalText: String,
    val translatedText: String,
    val fromLanguage: String,
    val toLanguage: String
)

/**
 * 翻译结果封装类
 */
sealed class TranslationResult {
    object Loading : TranslationResult()
    data class Success(val response: TranslationResponse) : TranslationResult()
    data class Error(val message: String) : TranslationResult()
}

/**
 * 翻译模型数据类
 */
data class TranslationModel(
    val id: Int,
    val createAt: String,
    val updateAt: String,
    val type: Int,
    val name: String,
    val enable: Int,
    val deeplxUrl: String ?,
    val microsoftKey: String ?,
    val googleNokeyUrl: String ?,
    val googleExtensionUrl: String ?,
    val googleExtensionKey: String ?,
    val googleXREFKey: String ?,
    val weight: Int
)

/**
 * 翻译模型列表响应数据类
 */
data class TranslationModelListResponse(
    val code: Int,
    val time: Long,
    val msg: String,
    val success: Boolean,
    val data: List<TranslationModel>
)

/**
 * 翻译模型列表结果封装类
 */
sealed class TranslationModelListResult {
    object Loading : TranslationModelListResult()
    data class Success(val response: TranslationModelListResponse) : TranslationModelListResult()
    data class Error(val message: String) : TranslationModelListResult()
}

/**
 * 翻译搜索请求数据类
 */
data class TranslateSearchRequest(
    val text: String,
    val source_lang: String,
    val target_lang: String,
    val translateApi: TranslationModel
)

/**
 * 微软翻译请求体数据类
 */
data class MicrosoftTranslateRequestBody(
    val text: String
)

/**
 * 微软翻译响应数据类
 */
data class MicrosoftTranslateResponse(
    val translations: List<MicrosoftTranslation>
)

data class MicrosoftTranslation(
    val text: String,
    val to: String
)

/**
 * Google翻译响应数据类
 */
data class GoogleTranslateResponse(
    val translations: List<GoogleTranslation>
)

data class GoogleTranslation(
    val text: String,
    val to: String
)

/**
 * Google Chrome扩展翻译响应数据类
 */
data class GoogleChromeExtensionResponse(
    val translateResponse: GoogleChromeTranslateResponse
)

data class GoogleChromeTranslateResponse(
    val translateText: String
)

/**
 * 统一翻译结果数据类
 */
data class TranslateSearchResponse(
    val code: Int,
    val data: String
)

/**
 * 翻译搜索结果封装类
 */
sealed class TranslateSearchResult {
    object Loading : TranslateSearchResult()
    data class Success(val response: TranslateSearchResponse) : TranslateSearchResult()
    data class Error(val message: String) : TranslateSearchResult()
}
