package com.greenterp

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.Gravity
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.greenterp.network.LoginResult
import com.greenterp.ui.theme.TerpMetaAndroidTheme
import com.greenterp.viewmodel.LoginViewModel

class LoginActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            TerpMetaAndroidTheme {
                LoginScreen()
            }
        }
    }
}

/**
 * 显示顶部Toast
 */
fun showTopToast(context: android.content.Context, message: String) {
    val toast = Toast.makeText(context, message, Toast.LENGTH_SHORT)
    toast.setGravity(Gravity.TOP or Gravity.CENTER_HORIZONTAL, 0, 200)
    toast.show()
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen() {
    val context = LocalContext.current
    val viewModel: LoginViewModel = viewModel()
    val focusManager = LocalFocusManager.current

    // 观察ViewModel状态
    val email by viewModel.email.collectAsState()
    val password by viewModel.password.collectAsState()
    val loginState by viewModel.loginState.collectAsState()
    val rememberPassword by viewModel.rememberPassword.collectAsState()

    // 初始化凭据管理器
    LaunchedEffect(Unit) {
        viewModel.initCredentialsManager(context)
    }

    // 处理登录结果
    LaunchedEffect(loginState) {
        when (val currentState = loginState) {
            is LoginResult.Success -> {
                if (currentState.response.success) {
                    // Login successful, navigate to main page
                    showTopToast(context, "Login successful")
                    val intent = Intent(context, MainActivity::class.java)
                    context.startActivity(intent)
                    (context as? LoginActivity)?.finish()
                } else {
                    // Login failed, show error message
                    val errorMessage = currentState.response.msg ?: "Login failed"
                    if (errorMessage.isNotEmpty() && errorMessage != "Please enter login information") {
                        showTopToast(context, errorMessage)
                    }
                }
            }
            is LoginResult.Error -> {
                showTopToast(context, currentState.message)
            }
            is LoginResult.Loading -> {
                // 加载状态在UI中处理
            }
        }
    }
    
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = Color.White
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color.White)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = {
                            // 点击空白区域时清除焦点，关闭键盘
                            focusManager.clearFocus()
                        }
                    )
                }
        ) {
        Row(
            modifier = Modifier.fillMaxSize()
        ) {
            // 左侧登录表单区域
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(horizontal = 60.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // 标题
                Text(
                    text = "TERP MATE",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF07c160),
                    letterSpacing = 2.sp,
                    modifier = Modifier.padding(bottom = 40.dp)
                )
                
                // 邮箱输入框
                OutlinedTextField(
                    value = email,
                    onValueChange = { viewModel.updateEmail(it) },
                    placeholder = { 
                        Text(
                            text = "Email",
                            color = Color(0xFF999999)
                        ) 
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFF07c160),
                        unfocusedBorderColor = Color(0xFFE0E0E0),
                        focusedLabelColor = Color(0xFF07c160)
                    ),
                    shape = RoundedCornerShape(4.dp),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email)
                )
                
                // 密码输入框
                OutlinedTextField(
                    value = password,
                    onValueChange = { viewModel.updatePassword(it) },
                    placeholder = { 
                        Text(
                            text = "Password",
                            color = Color(0xFF999999)
                        ) 
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFF07c160),
                        unfocusedBorderColor = Color(0xFFE0E0E0),
                        focusedLabelColor = Color(0xFF07c160)
                    ),
                    shape = RoundedCornerShape(4.dp),
                    visualTransformation = PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password)
                )

                // 记住密码复选框
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp)
                        .clickable { viewModel.updateRememberPassword(!rememberPassword) },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(20.dp)
                            .background(
                                color = if (rememberPassword) Color(0xFF07c160) else Color.Transparent,
                                shape = RoundedCornerShape(4.dp)
                            )
                            .then(
                                if (!rememberPassword) {
                                    Modifier.background(
                                        color = Color.Transparent,
                                        shape = RoundedCornerShape(4.dp)
                                    ).then(
                                        Modifier.background(
                                            color = Color(0xFFE0E0E0),
                                            shape = RoundedCornerShape(4.dp)
                                        )
                                    )
                                } else Modifier
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        if (rememberPassword) {
                            Icon(
                                Icons.Default.Check,
                                contentDescription = "Checked",
                                tint = Color.White,
                                modifier = Modifier.size(14.dp)
                            )
                        }
                    }

                    Text(
                        text = "Remember Password",
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }

                // 登录按钮
                Button(
                    onClick = {
                        viewModel.login()
                    },
                    enabled = loginState !is LoginResult.Loading,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF07c160)
                    ),
                    shape = RoundedCornerShape(4.dp)
                ) {
                    if (loginState is LoginResult.Loading) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                            Text(
                                text = "Logging in...",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    } else {
                        Text(
                            text = "Login",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
                
                // 底部链接
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 24.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TextButton(
                        onClick = {
                            // 打开忘记密码网页
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://admin.gtmeeting.com/#/findPwd"))
                            context.startActivity(intent)
                        }
                    ) {
                        Text(
                            text = "Forgot Password?",
                            color = Color(0xFF666666),
                            fontSize = 14.sp
                        )
                    }

                    TextButton(
                        onClick = {
                            // 打开注册网页
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://admin.gtmeeting.com/#/register"))
                            context.startActivity(intent)
                        }
                    ) {
                        Text(
                            text = "Sign Up",
                            color = Color(0xFF666666),
                            fontSize = 14.sp
                        )
                    }
                }
            }
            
            // 右侧绿色区域
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .background(Color(0xFF07c160)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 灯泡图片
                    Image(
                        painter = painterResource(id = R.drawable.bg),
                        contentDescription = "Logo",
                        modifier = Modifier
                            .size(200.dp)
                            .padding(bottom = 24.dp),
                        contentScale = ContentScale.Fit
                    )
                    
                    // 标语文字
                    Text(
                        text = "Interpreting Brilliance, Made Simple",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    Text(
                        text = "Powered by Green Terp AI",
                        color = Color.White,
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        }
    }
}



@Preview(showBackground = true, widthDp = 1024, heightDp = 768)
@Composable
fun LoginScreenPreview() {
    TerpMetaAndroidTheme {
        LoginScreen()
    }
}
