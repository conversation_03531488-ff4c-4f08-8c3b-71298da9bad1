package com.greenterp.ui.voice

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.greenterp.data.VoiceSession
import com.greenterp.GlossaryManager
import com.greenterp.ReplaceManager

/**
 * 语音识别显示组件
 * 显示语音识别会话列表
 */
@Composable
fun VoiceRecognitionDisplay(
    sessions: List<VoiceSession>,
    fontSize: Int = 16,
    isDarkTheme: Boolean = false,
    searchValue: String = "",
    glossaryManager: GlossaryManager? = null, // 词汇表管理器
    replaceManager: ReplaceManager? = null, // 新增：替换表管理器
    onStylusTextSelected: ((String) -> Unit)? = null, // 新增：触控笔划词回调
    highlightColor: androidx.compose.ui.graphics.Color = androidx.compose.ui.graphics.Color.Red, // 新增：高亮颜色
    modifier: Modifier = Modifier,
    listState: LazyListState = rememberLazyListState()
) {
    LazyColumn(
        modifier = modifier
            .background(if (isDarkTheme) Color(0xFF1E1E1E) else Color.White),
        state = listState,
        contentPadding = PaddingValues(0.dp),
        reverseLayout = true // 反转布局，最新内容在顶部
    ) {
        if (sessions.isEmpty()) {
//            item {
//                Box(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .fillParentMaxHeight(),
//                    contentAlignment = Alignment.Center
//                ) {
//                    Text(
//                        text = "点击 Start 按钮开始语音识别",
//                        color = if (isDarkTheme) Color(0xFF999999) else Color(0xFF999999),
//                        fontSize = fontSize.sp,
//                        textAlign = TextAlign.Center
//                    )
//                }
//            }
        } else {
            items(sessions.size) { index ->
                val session = sessions[index]
                VoiceSessionItem(
                    session = session,
                    isCurrentSession = index == 0,
                    fontSize = fontSize,
                    isDarkTheme = isDarkTheme,
                    searchValue = searchValue,
                    glossaryManager = glossaryManager, // 传递词汇表管理器
                    replaceManager = replaceManager, // 传递替换表管理器
                    onStylusTextSelected = onStylusTextSelected, // 传递触控笔划词回调
                    highlightColor = highlightColor, // 传递高亮颜色
                    modifier = Modifier
                        .fillParentMaxHeight() // 每个会话区域都占满屏幕高度
                        .padding(horizontal = 16.dp, vertical = 10.dp)
                )
            }
        }
    }
}
