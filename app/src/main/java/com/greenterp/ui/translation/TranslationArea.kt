package com.greenterp.ui.translation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.greenterp.data.TranslationSession


/**
 * 翻译区域组件
 * 显示翻译会话列表，结构与VoiceRecognitionDisplay一致
 */
@Composable
fun TranslationArea(
    sessions: List<TranslationSession>,
    fontSize: Int = 16,
    isDarkTheme: Boolean = false,
    modifier: Modifier = Modifier,
    listState: LazyListState = rememberLazyListState()
) {
    LazyColumn(
        modifier = modifier
            .background(if (isDarkTheme) Color(0xFF1E1E1E) else Color.White),
        state = listState,
        contentPadding = PaddingValues(0.dp),
        reverseLayout = true // 反转布局，最新内容在底部
    ) {
        if (sessions.isEmpty()) {
            // 空状态 - 与VoiceRecognitionDisplay保持一致，注释掉空状态显示
        } else {
            // 显示所有翻译会话，每个会话占满整个面板区域
            items(sessions.size) { index ->
                val session = sessions[index]
                TranslationSessionItem(
                    session = session,
                    isCurrentSession = index == 0, // 最新会话在开头（reverseLayout=true时实际在底部）
                    fontSize = fontSize,
                    isDarkTheme = isDarkTheme,
                    modifier = Modifier
                        .fillParentMaxHeight() // 每个会话占满整个面板高度
                        .padding(horizontal = 16.dp)
                )
            }
        }
    }
}
