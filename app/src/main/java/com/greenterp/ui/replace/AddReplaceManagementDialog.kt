package com.greenterp.ui.replace

import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.greenterp.data.ReplaceManagementSaveResult
import com.greenterp.network.ApiService
import kotlinx.coroutines.launch

/**
 * 添加/编辑替换表管理对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddReplaceManagementDialog(
    sceneId: Int,
    onDismiss: () -> Unit,
    onSaveSuccess: () -> Unit,
    initialReplaceA: String = "", // 用于编辑模式的初始A值
    initialReplaceB: String = "", // 用于编辑模式的初始B值
    isEditMode: Boolean = false, // 是否为编辑模式
    itemId: Int? = null // 编辑模式下的项目ID
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }

    var replaceA by remember { mutableStateOf(initialReplaceA) }
    var replaceB by remember { mutableStateOf(initialReplaceB) }
    var saveResult by remember { mutableStateOf<ReplaceManagementSaveResult?>(null) }
    var isReplaceAError by remember { mutableStateOf(false) }
    var isReplaceBError by remember { mutableStateOf(false) }
    
    val userId = apiService.getCurrentUserId()
    
    // 保存替换表管理
    fun saveReplaceManagement() {
        // 验证输入
        isReplaceAError = replaceA.isBlank()
        isReplaceBError = replaceB.isBlank()
        
        if (isReplaceAError || isReplaceBError) {
            return
        }

        coroutineScope.launch {
            saveResult = ReplaceManagementSaveResult.Loading
            val result = apiService.saveReplaceManagement(replaceA, replaceB, sceneId, userId, if (isEditMode) itemId else null)
            saveResult = result

            when (result) {
                is ReplaceManagementSaveResult.Success -> {
                    val message = if (isEditMode) "Edit successful" else "Add successful"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    onSaveSuccess()
                    onDismiss()
                }
                is ReplaceManagementSaveResult.Error -> {
                    val message = if (isEditMode) "Edit failed: ${result.message}" else "Add failed: ${result.message}"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                }
                else -> {}
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            modifier = Modifier
                .width(400.dp)
                .wrapContentHeight(),
            shape = RoundedCornerShape(12.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = if (isEditMode) "Edit Force Replace" else "Add Force Replace",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // A输入框
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "* A",
                        fontSize = 14.sp,
                        color = Color.Red,
                        modifier = Modifier.width(40.dp)
                    )
                    OutlinedTextField(
                        value = replaceA,
                        onValueChange = { 
                            replaceA = it
                            isReplaceAError = false
                        },
                        isError = isReplaceAError,
                        modifier = Modifier.weight(1f),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFF07c160),
                            focusedLabelColor = Color(0xFF07c160)
                        )
                    )
                }

                // B输入框
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "* B",
                        fontSize = 14.sp,
                        color = Color.Red,
                        modifier = Modifier.width(40.dp)
                    )
                    OutlinedTextField(
                        value = replaceB,
                        onValueChange = { 
                            replaceB = it
                            isReplaceBError = false
                        },
                        isError = isReplaceBError,
                        modifier = Modifier.weight(1f),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFF07c160),
                            focusedLabelColor = Color(0xFF07c160)
                        )
                    )
                }

                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        )
                    ) {
                        Text("Cancel")
                    }

                    // 保存按钮
                    Button(
                        onClick = { saveReplaceManagement() },
                        enabled = saveResult !is ReplaceManagementSaveResult.Loading,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        )
                    ) {
                        if (saveResult is ReplaceManagementSaveResult.Loading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text(
                                text = "Save",
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}
