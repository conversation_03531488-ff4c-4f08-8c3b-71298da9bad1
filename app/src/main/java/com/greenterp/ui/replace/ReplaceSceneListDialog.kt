package com.greenterp.ui.replace

import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.greenterp.data.*
import com.greenterp.network.ApiService
import com.greenterp.utils.SettingsManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * 替换表列表对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReplaceSceneListDialog(
    onDismiss: () -> Unit,
    onReplaceSceneSelected: (ReplaceSceneData) -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }
    val settingsManager = remember { SettingsManager(context) }

    // 状态变量
    var replaceSceneListResult by remember { mutableStateOf<ReplaceSceneListResult>(ReplaceSceneListResult.Loading) }
    var allReplaceSceneResult by remember { mutableStateOf<ReplaceSceneListAllResult>(ReplaceSceneListAllResult.Loading) }
    var searchText by remember { mutableStateOf("") }
    // 从设置中恢复选择的替换表
    var selectedList by remember { mutableStateOf(settingsManager.selectedReplaceName) }
    var isDropdownExpanded by remember { mutableStateOf(false) }
    var currentPage by remember { mutableStateOf(0) }
    var totalCount by remember { mutableStateOf(0) }
    var showAddDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showManagementDialog by remember { mutableStateOf(false) }
    var itemToEdit by remember { mutableStateOf<ReplaceSceneData?>(null) }
    var itemToDelete by remember { mutableStateOf<ReplaceSceneData?>(null) }
    var itemToManage by remember { mutableStateOf<ReplaceSceneData?>(null) }

    val currentAllReplaceSceneResult = allReplaceSceneResult
    val listOptions = when (currentAllReplaceSceneResult) {
        is ReplaceSceneListAllResult.Success -> listOf("None") + currentAllReplaceSceneResult.replaceSceneList.map { it.name }
        else -> listOf("None")
    }
    val pageSize = 6
    val userId = apiService.getCurrentUserId()

    // 执行搜索的函数
    fun performSearch() {
        coroutineScope.launch {
            replaceSceneListResult = ReplaceSceneListResult.Loading
            val searchInfo = if (searchText.isNotBlank()) searchText else null
            replaceSceneListResult = apiService.getReplaceSceneList(currentPage, pageSize, userId, searchInfo)
            if (replaceSceneListResult is ReplaceSceneListResult.Success) {
                totalCount = (replaceSceneListResult as ReplaceSceneListResult.Success).replaceSceneList.count
                Log.d("ReplaceSceneListDialog", "加载替换表列表成功，共 ${totalCount} 条记录")
            } else if (replaceSceneListResult is ReplaceSceneListResult.Error) {
                Log.e("ReplaceSceneListDialog", "加载替换表列表失败: ${(replaceSceneListResult as ReplaceSceneListResult.Error).message}")
            }
        }
    }

    // 初始加载所有替换表（用于下拉框）
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            allReplaceSceneResult = apiService.getAllReplaceSceneList(userId)
        }
    }

    // 页码变化时重新搜索（包括初始加载）
    LaunchedEffect(currentPage) {
        performSearch()
    }

    // 删除替换表
    fun deleteReplaceScene(item: ReplaceSceneData) {
        coroutineScope.launch {
            val result = apiService.deleteReplaceScene(item.id)
            when (result) {
                is ReplaceSceneDeleteResult.Success -> {
                    Toast.makeText(context, "Delete successful", Toast.LENGTH_SHORT).show()
                    // 删除成功后刷新列表和下拉框数据
                    performSearch()
                    allReplaceSceneResult = apiService.getAllReplaceSceneList(userId)
                }
                is ReplaceSceneDeleteResult.Error -> {
                    Toast.makeText(context, "Delete failed: ${result.message}", Toast.LENGTH_SHORT).show()
                }
                else -> {}
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 顶部区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back 按钮
                    TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF07c160)
                        )
                    ) {
                        Icon(
                            Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Back", fontSize = 14.sp)
                    }

                    Spacer(modifier = Modifier.weight(1f))

                    // 标题
                    Text(
                        text = "Force Replace List",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.weight(1f))
                }

                // 搜索和操作区域 - 所有元素在同一行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 搜索输入框 - 固定宽度
                    OutlinedTextField(
                        value = searchText,
                        onValueChange = { searchText = it },
                        placeholder = { Text("Search Info", fontSize = 14.sp) },
                        modifier = Modifier.width(200.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFFE0E0E0),
                            unfocusedBorderColor = Color(0xFFE0E0E0)
                        )
                    )

                    // Search 按钮
                    Button(
                        onClick = {
                            // 重置到第一页并执行搜索
                            currentPage = 0
                            performSearch()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color.Black
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Search", fontSize = 14.sp)
                    }

                    // Add 按钮
                    Button(
                        onClick = { showAddDialog = true },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Add", fontSize = 14.sp, color = Color.White)
                    }

                    // Choose List 标签
                    Text(
                        text = "Choose List:",
                        fontSize = 14.sp,
                        modifier = Modifier.padding(start = 16.dp, end = 8.dp)
                    )

                    // Choose List 下拉框
                    ExposedDropdownMenuBox(
                        expanded = isDropdownExpanded,
                        onExpandedChange = { isDropdownExpanded = it },
                        modifier = Modifier.width(300.dp) // 调整宽度为原来的两倍
                    ) {
                        OutlinedTextField(
                            value = selectedList.ifEmpty { "Select" },
                            onValueChange = {},
                            readOnly = true,
                            trailingIcon = {
                                Icon(
                                    Icons.Default.ArrowDropDown,
                                    contentDescription = "Dropdown"
                                )
                            },
                            modifier = Modifier
                                .menuAnchor()
                                .fillMaxWidth(),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFFE0E0E0),
                                unfocusedBorderColor = Color(0xFFE0E0E0)
                            )
                        )

                        ExposedDropdownMenu(
                            expanded = isDropdownExpanded,
                            onDismissRequest = { isDropdownExpanded = false }
                        ) {
                            if (listOptions.isEmpty()) {
                                DropdownMenuItem(
                                    text = {
                                        Text(
                                            "Loading...",
                                            fontSize = 14.sp,
                                            color = Color.Gray
                                        )
                                    },
                                    onClick = { }
                                )
                            } else {
                                listOptions.forEach { option ->
                                    DropdownMenuItem(
                                        text = { Text(option, fontSize = 14.sp) },
                                        onClick = {
                                            selectedList = option
                                            isDropdownExpanded = false

                                            if (option == "None") {
                                                // 清空选择
                                                settingsManager.selectedReplaceName = ""
                                                settingsManager.selectedReplaceId = -1
                                                Log.d("ReplaceSceneListDialog", "清空替换表选择")

                                                // 创建一个空的替换表对象来清空数据
                                                val emptyReplaceScene = ReplaceSceneData(
                                                    id = -1,
                                                    createAt = "",
                                                    updateAt = null,
                                                    name = "",
                                                    userId = ""
                                                )
                                                onReplaceSceneSelected(emptyReplaceScene)
                                            } else {
                                                // 获取选中替换表的完整数据并调用回调
                                                when (currentAllReplaceSceneResult) {
                                                    is ReplaceSceneListAllResult.Success -> {
                                                        val replaceScene = currentAllReplaceSceneResult.replaceSceneList.find { it.name == option }
                                                        replaceScene?.let {
                                                            // 保存选择到设置中
                                                            settingsManager.selectedReplaceName = it.name
                                                            settingsManager.selectedReplaceId = it.id
                                                            Log.d("ReplaceSceneListDialog", "下拉框选择替换表: ${it.name}, ID: ${it.id}")

                                                            // 触发选择回调，但不关闭对话框
                                                            onReplaceSceneSelected(it)
                                                        }
                                                    }
                                                    else -> {}
                                                }
                                            }
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                // 表格区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    // 表头
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xFFF8F9FA))
                            .border(1.dp, Color(0xFFE0E0E0))
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Index",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(1f)
                        )
                        Text(
                            text = "Name",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(2f)
                        )
                        Text(
                            text = "Operate",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(3f),
                            textAlign = TextAlign.Center
                        )
                    }

                    // 表格内容
                    val currentResult = replaceSceneListResult
                    when (currentResult) {
                        is ReplaceSceneListResult.Loading -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    color = Color(0xFF07c160)
                                )
                            }
                        }
                        is ReplaceSceneListResult.Error -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "Load failed: ${currentResult.message}",
                                    color = Color.Red,
                                    fontSize = 14.sp
                                )
                            }
                        }
                        is ReplaceSceneListResult.Success -> {
                            LazyColumn {
                                items(currentResult.replaceSceneList.data.size) { index ->
                                    val item = currentResult.replaceSceneList.data[index]
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .border(
                                                width = 1.dp,
                                                color = Color(0xFFE0E0E0)
                                            )
                                            .padding(12.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = (currentPage * pageSize + index + 1).toString(),
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = item.name,
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(2f)
                                        )

                                        // 操作按钮
                                        Row(
                                            modifier = Modifier.weight(4f),
                                            horizontalArrangement = Arrangement.spacedBy(6.dp, Alignment.CenterHorizontally)
                                        ) {
                                            Button(
                                                onClick = {
                                                    itemToManage = item
                                                    showManagementDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFF5F5F5),
                                                    contentColor = Color.Black
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Management", fontSize = 12.sp)
                                            }

                                            Button(
                                                onClick = {
                                                    itemToEdit = item
                                                    showEditDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFF5F5F5),
                                                    contentColor = Color.Black
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Edit", fontSize = 12.sp)
                                            }

                                            Button(
                                                onClick = {
                                                    itemToDelete = item
                                                    showDeleteDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFFF4444),
                                                    contentColor = Color.White
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Delete", fontSize = 12.sp)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 底部分页器
                val currentResult = replaceSceneListResult
                if (currentResult is ReplaceSceneListResult.Success) {
                    val totalPages = kotlin.math.ceil(totalCount.toDouble() / pageSize).toInt()

                    if (totalPages > 1) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp, bottom = 24.dp),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 上一页箭头
                            IconButton(
                                onClick = { if (currentPage > 0) currentPage-- },
                                enabled = currentPage > 0,
                                modifier = Modifier.size(32.dp)
                            ) {
                                Text(
                                    text = "‹",
                                    fontSize = 18.sp,
                                    color = if (currentPage > 0) Color(0xFF07c160) else Color.Gray
                                )
                            }

                            Spacer(modifier = Modifier.width(8.dp))

                            // 页码信息
                            Text(
                                text = "${currentPage + 1} / $totalPages",
                                fontSize = 14.sp,
                                color = Color.Gray
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            // 下一页箭头
                            IconButton(
                                onClick = { if (currentPage < totalPages - 1) currentPage++ },
                                enabled = currentPage < totalPages - 1,
                                modifier = Modifier.size(32.dp)
                            ) {
                                Text(
                                    text = "›",
                                    fontSize = 18.sp,
                                    color = if (currentPage < totalPages - 1) Color(0xFF07c160) else Color.Gray
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // 添加替换表对话框
    if (showAddDialog) {
        AddReplaceSceneDialog(
            onDismiss = { showAddDialog = false },
            onSaveSuccess = {
                // 保存成功后刷新列表和下拉框数据
                performSearch()
                coroutineScope.launch {
                    allReplaceSceneResult = apiService.getAllReplaceSceneList(userId)
                }
            }
        )
    }

    // 编辑替换表对话框
    if (showEditDialog && itemToEdit != null) {
        AddReplaceSceneDialog(
            onDismiss = {
                showEditDialog = false
                itemToEdit = null
            },
            onSaveSuccess = {
                // 保存成功后刷新列表和下拉框数据
                performSearch()
                coroutineScope.launch {
                    allReplaceSceneResult = apiService.getAllReplaceSceneList(userId)
                }
            },
            initialName = itemToEdit?.name ?: "",
            isEditMode = true,
            itemId = itemToEdit?.id
        )
    }

    // 删除确认对话框
    if (showDeleteDialog && itemToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteDialog = false
                itemToDelete = null
            },
            title = {
                Text(
                    text = "Delete Confirmation",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to delete \"${itemToDelete?.name}\"? This action cannot be undone.",
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        itemToDelete?.let { deleteReplaceScene(it) }
                        showDeleteDialog = false
                        itemToDelete = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFFF4444)
                    )
                ) {
                    Text("Delete", color = Color.White)
                }
            },
            dismissButton = {
                OutlinedButton(
                    onClick = {
                        showDeleteDialog = false
                        itemToDelete = null
                    }
                ) {
                    Text("Cancel")
                }
            }
        )
    }

    // 替换表管理对话框
    if (showManagementDialog && itemToManage != null) {
        ReplaceManagementDialog(
            replaceSceneName = itemToManage?.name ?: "",
            sceneId = itemToManage?.id ?: 0,
            onDismiss = {
                showManagementDialog = false
                itemToManage = null
            }
        )
    }
}
