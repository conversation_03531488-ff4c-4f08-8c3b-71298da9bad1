package com.greenterp.ui.replace

import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.greenterp.data.ReplaceSceneSaveResult
import com.greenterp.network.ApiService
import kotlinx.coroutines.launch

/**
 * 添加/编辑替换表对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddReplaceSceneDialog(
    onDismiss: () -> Unit,
    onSaveSuccess: () -> Unit,
    initialName: String = "", // 用于编辑模式的初始名称
    isEditMode: Boolean = false, // 是否为编辑模式
    itemId: Int? = null // 编辑模式下的项目ID
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }

    var replaceSceneName by remember { mutableStateOf(initialName) }
    var saveResult by remember { mutableStateOf<ReplaceSceneSaveResult?>(null) }
    var isNameError by remember { mutableStateOf(false) }
    
    val userId = apiService.getCurrentUserId()
    
    // 保存替换表
    fun saveReplaceScene() {
        if (replaceSceneName.isBlank()) {
            isNameError = true
            return
        }

        isNameError = false
        coroutineScope.launch {
            saveResult = ReplaceSceneSaveResult.Loading
            val result = apiService.saveReplaceScene(replaceSceneName, userId, if (isEditMode) itemId else null)
            saveResult = result

            when (result) {
                is ReplaceSceneSaveResult.Success -> {
                    val message = if (isEditMode) "Edit successful" else "Add successful"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    onSaveSuccess()
                    onDismiss()
                }
                is ReplaceSceneSaveResult.Error -> {
                    val message = if (isEditMode) "Edit failed: ${result.message}" else "Add failed: ${result.message}"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                }
                else -> {}
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            modifier = Modifier
                .width(400.dp)
                .wrapContentHeight(),
            shape = RoundedCornerShape(12.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = if (isEditMode) "Edit Replace Scene" else "Add Replace Scene",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // 名称输入框
                OutlinedTextField(
                    value = replaceSceneName,
                    onValueChange = { 
                        replaceSceneName = it
                        isNameError = false
                    },
                    label = { Text("Name") },
                    isError = isNameError,
                    supportingText = if (isNameError) {
                        { Text("Name cannot be empty", color = MaterialTheme.colorScheme.error) }
                    } else null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFF07c160),
                        focusedLabelColor = Color(0xFF07c160)
                    )
                )

                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        )
                    ) {
                        Text("Cancel")
                    }

                    // 保存按钮
                    Button(
                        onClick = { saveReplaceScene() },
                        enabled = saveResult !is ReplaceSceneSaveResult.Loading,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        )
                    ) {
                        if (saveResult is ReplaceSceneSaveResult.Loading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text(
                                text = if (isEditMode) "Update" else "Save",
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}
