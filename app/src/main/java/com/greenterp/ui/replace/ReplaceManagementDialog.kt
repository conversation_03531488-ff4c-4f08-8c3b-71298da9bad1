package com.greenterp.ui.replace

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import com.greenterp.data.ReplaceManagementListResult
import com.greenterp.data.ReplaceManagementDeleteResult
import com.greenterp.data.ReplaceManagementExportResult
import com.greenterp.data.ReplaceManagementImportResult
import com.greenterp.data.ReplaceManagementData
import com.greenterp.network.ApiService
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream

/**
 * 替换表管理对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReplaceManagementDialog(
    replaceSceneName: String,
    sceneId: Int,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }

    var searchText by remember { mutableStateOf("") }
    var replaceManagementResult by remember { mutableStateOf<ReplaceManagementListResult>(ReplaceManagementListResult.Loading) }
    var currentPage by remember { mutableStateOf(0) }
    var totalCount by remember { mutableStateOf(0) }
    var showAddDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var itemToEdit by remember { mutableStateOf<ReplaceManagementData?>(null) }
    var itemToDelete by remember { mutableStateOf<ReplaceManagementData?>(null) }
    var exportResult by remember { mutableStateOf<ReplaceManagementExportResult?>(null) }
    var importResult by remember { mutableStateOf<ReplaceManagementImportResult?>(null) }

    val pageSize = 10

    // 执行搜索的函数
    fun performSearch() {
        coroutineScope.launch {
            replaceManagementResult = ReplaceManagementListResult.Loading
            val searchInfo = if (searchText.isNotBlank()) searchText else null
            // 实时获取userId，确保使用当前登录用户的数据
            val userId = apiService.getCurrentUserId()
            replaceManagementResult = apiService.getReplaceManagementList(currentPage, pageSize, sceneId, userId, searchInfo)
            if (replaceManagementResult is ReplaceManagementListResult.Success) {
                totalCount = (replaceManagementResult as ReplaceManagementListResult.Success).replaceList.count
            }
        }
    }

    // 文件保存launcher
    val saveFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("application/vnd.ms-excel")
    ) { uri ->
        uri?.let { fileUri ->
            exportResult?.let { result ->
                if (result is ReplaceManagementExportResult.Success) {
                    try {
                        context.contentResolver.openOutputStream(fileUri)?.use { outputStream ->
                            outputStream.write(result.fileBytes)
                            outputStream.flush()
                            Toast.makeText(context, "File saved successfully", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Toast.makeText(context, "Failed to save file: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }

    // 文件选择launcher
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { fileUri ->
            try {
                context.contentResolver.openInputStream(fileUri)?.use { inputStream ->
                    val fileBytes = inputStream.readBytes()
                    val fileName = "ForceReplace.xls"

                    coroutineScope.launch {
                        importResult = ReplaceManagementImportResult.Loading
                        // 实时获取userId，确保使用当前登录用户的数据
                        val userId = apiService.getCurrentUserId()
                        val result = apiService.importReplaceManagement(fileBytes, fileName, userId, sceneId)
                        importResult = result

                        when (result) {
                            is ReplaceManagementImportResult.Success -> {
                                Toast.makeText(context, "Import successful", Toast.LENGTH_SHORT).show()
                                performSearch() // 刷新列表
                            }
                            is ReplaceManagementImportResult.Error -> {
                                Toast.makeText(context, "Import failed: ${result.message}", Toast.LENGTH_SHORT).show()
                            }
                            else -> {}
                        }
                    }
                }
            } catch (e: Exception) {
                Toast.makeText(context, "Failed to read file: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // 删除替换表管理
    fun deleteReplaceManagement(item: ReplaceManagementData) {
        coroutineScope.launch {
            // 实时获取userId，确保使用当前登录用户的数据
            val userId = apiService.getCurrentUserId()
            val result = apiService.deleteReplaceManagement(listOf(item.id), userId)
            when (result) {
                is ReplaceManagementDeleteResult.Success -> {
                    Toast.makeText(context, "Delete successful", Toast.LENGTH_SHORT).show()
                    // 删除成功后刷新列表
                    performSearch()
                }
                is ReplaceManagementDeleteResult.Error -> {
                    Toast.makeText(context, "Delete failed: ${result.message}", Toast.LENGTH_SHORT).show()
                }
                else -> {}
            }
        }
    }

    // 页码变化时重新搜索（包括初始加载）
    LaunchedEffect(currentPage) {
        performSearch()
    }

    // 搜索文本变化时的防抖搜索
    LaunchedEffect(searchText) {
        if (searchText.isNotEmpty()) {
            delay(500) // 防抖延迟500ms
            if (currentPage != 0) {
                currentPage = 0 // 重置到第一页，这会触发上面的LaunchedEffect
            } else {
                performSearch() // 如果已经在第一页，直接搜索
            }
        } else {
            // 搜索框清空时立即搜索
            if (currentPage != 0) {
                currentPage = 0 // 重置到第一页，这会触发上面的LaunchedEffect
            } else {
                performSearch() // 如果已经在第一页，直接搜索
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 顶部区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back 按钮
                    TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF07c160)
                        )
                    ) {
                        Icon(
                            Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Back", fontSize = 14.sp)
                    }
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 标题
                    Text(
                        text = "Force Replace($replaceSceneName)",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                }

                // 搜索和操作区域 - 所有元素在同一行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 搜索输入框 - 固定宽度
                    OutlinedTextField(
                        value = searchText,
                        onValueChange = { searchText = it },
                        placeholder = { Text("Search Info", fontSize = 14.sp) },
                        modifier = Modifier.width(200.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFFE0E0E0),
                            unfocusedBorderColor = Color(0xFFE0E0E0)
                        )
                    )

                    // Search 按钮
                    Button(
                        onClick = {
                            // 重置到第一页并执行搜索
                            currentPage = 0
                            performSearch()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color.Black
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Search", fontSize = 14.sp)
                    }

                    // Add 按钮
                    Button(
                        onClick = { showAddDialog = true },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Add", fontSize = 14.sp, color = Color.White)
                    }

                    // Upload 按钮
                    Button(
                        onClick = {
                            filePickerLauncher.launch("application/vnd.ms-excel")
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Upload", fontSize = 14.sp, color = Color.White)
                    }

                    // Download 按钮
                    Button(
                        onClick = {
                            coroutineScope.launch {
                                exportResult = ReplaceManagementExportResult.Loading
                                // 实时获取userId，确保使用当前登录用户的数据
                                val userId = apiService.getCurrentUserId()
                                val result = apiService.exportReplaceManagement(sceneId, userId)
                                exportResult = result

                                when (result) {
                                    is ReplaceManagementExportResult.Success -> {
                                        saveFileLauncher.launch("ForceReplace.xls")
                                    }
                                    is ReplaceManagementExportResult.Error -> {
                                        Toast.makeText(context, "Export failed: ${result.message}", Toast.LENGTH_SHORT).show()
                                    }
                                    else -> {}
                                }
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Download", fontSize = 14.sp, color = Color.White)
                    }
                }

                // 表格区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    // 表头
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xFFF8F9FA))
                            .border(1.dp, Color(0xFFE0E0E0))
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Index",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(1f)
                        )
                        Row(
                            modifier = Modifier.weight(2f),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Source",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Icon(
                                Icons.Default.KeyboardArrowUp,
                                contentDescription = "Sort",
                                modifier = Modifier.size(16.dp),
                                tint = Color.Gray
                            )
                        }
                        Text(
                            text = "Target",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(2f)
                        )
                        Row(
                            modifier = Modifier.weight(2f),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Create Date",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Icon(
                                Icons.Default.KeyboardArrowDown,
                                contentDescription = "Sort",
                                modifier = Modifier.size(16.dp),
                                tint = Color.Gray
                            )
                        }
                        Text(
                            text = "Operate",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(2f),
                            textAlign = TextAlign.Center
                        )
                    }

                    // 表格内容
                    val currentResult = replaceManagementResult
                    when (currentResult) {
                        is ReplaceManagementListResult.Loading -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    color = Color(0xFF07c160)
                                )
                            }
                        }
                        is ReplaceManagementListResult.Error -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "Load failed: ${currentResult.message}",
                                    color = Color.Red,
                                    fontSize = 14.sp
                                )
                            }
                        }
                        is ReplaceManagementListResult.Success -> {
                            val replaceItems = currentResult.replaceList.data
                            if (replaceItems.isEmpty()) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(200.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "No Data",
                                        color = Color.Gray,
                                        fontSize = 14.sp
                                    )
                                }
                            } else {
                                LazyColumn {
                                    itemsIndexed(replaceItems) { index, item ->
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .border(
                                                    width = 1.dp,
                                                    color = Color(0xFFE0E0E0)
                                                )
                                                .padding(12.dp),
                                            horizontalArrangement = Arrangement.SpaceBetween,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                text = (currentPage * pageSize + index + 1).toString(),
                                                fontSize = 14.sp,
                                                modifier = Modifier.weight(1f)
                                            )
                                            Text(
                                                text = item.replaceA,
                                                fontSize = 14.sp,
                                                modifier = Modifier.weight(2f)
                                            )
                                            Text(
                                                text = item.replaceB,
                                                fontSize = 14.sp,
                                                modifier = Modifier.weight(2f)
                                            )
                                            Text(
                                                text = item.createAt,
                                                fontSize = 14.sp,
                                                modifier = Modifier.weight(2f)
                                            )

                                            // 操作按钮
                                            Row(
                                                modifier = Modifier.weight(2f),
                                                horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally)
                                            ) {
                                                Button(
                                                    onClick = {
                                                        itemToEdit = item
                                                        showEditDialog = true
                                                    },
                                                    colors = ButtonDefaults.buttonColors(
                                                        containerColor = Color(0xFFF5F5F5),
                                                        contentColor = Color.Black
                                                    ),
                                                    shape = RoundedCornerShape(4.dp),
                                                    modifier = Modifier.height(32.dp)
                                                ) {
                                                    Text("Edit", fontSize = 12.sp)
                                                }

                                                Button(
                                                    onClick = {
                                                        itemToDelete = item
                                                        showDeleteDialog = true
                                                    },
                                                    colors = ButtonDefaults.buttonColors(
                                                        containerColor = Color(0xFFFF4444),
                                                        contentColor = Color.White
                                                    ),
                                                    shape = RoundedCornerShape(4.dp),
                                                    modifier = Modifier.height(32.dp)
                                                ) {
                                                    Text("Delete", fontSize = 12.sp)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 底部分页器
                val currentResult = replaceManagementResult
                if (currentResult is ReplaceManagementListResult.Success) {
                    val totalPages = kotlin.math.ceil(totalCount.toDouble() / pageSize).toInt()

                    if (totalPages > 1) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp, bottom = 24.dp),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 上一页箭头
                            IconButton(
                                onClick = { if (currentPage > 0) currentPage-- },
                                enabled = currentPage > 0,
                                modifier = Modifier.size(32.dp)
                            ) {
                                Text(
                                    text = "‹",
                                    fontSize = 18.sp,
                                    color = if (currentPage > 0) Color(0xFF07c160) else Color.Gray
                                )
                            }

                            Spacer(modifier = Modifier.width(8.dp))

                            // 页码信息
                            Text(
                                text = "${currentPage + 1} / $totalPages",
                                fontSize = 14.sp,
                                color = Color.Gray
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            // 下一页箭头
                            IconButton(
                                onClick = { if (currentPage < totalPages - 1) currentPage++ },
                                enabled = currentPage < totalPages - 1,
                                modifier = Modifier.size(32.dp)
                            ) {
                                Text(
                                    text = "›",
                                    fontSize = 18.sp,
                                    color = if (currentPage < totalPages - 1) Color(0xFF07c160) else Color.Gray
                                )
                            }
                        }
                    } else {
                        // 只有一页时显示页码"1"
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp, bottom = 24.dp),
                            horizontalArrangement = Arrangement.End
                        ) {
                            Text(
                                text = "1",
                                fontSize = 14.sp,
                                color = Color.Gray
                            )
                        }
                    }
                } else {
                    // 无数据时也显示页码"1"
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 16.dp, bottom = 24.dp),
                        horizontalArrangement = Arrangement.End
                    ) {
                        Text(
                            text = "1",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
        }
    }

    // 添加替换表管理对话框
    if (showAddDialog) {
        AddReplaceManagementDialog(
            sceneId = sceneId,
            onDismiss = { showAddDialog = false },
            onSaveSuccess = {
                // 保存成功后刷新列表
                performSearch()
            }
        )
    }

    // 编辑替换表管理对话框
    if (showEditDialog && itemToEdit != null) {
        AddReplaceManagementDialog(
            sceneId = sceneId,
            onDismiss = {
                showEditDialog = false
                itemToEdit = null
            },
            onSaveSuccess = {
                // 保存成功后刷新列表
                performSearch()
            },
            initialReplaceA = itemToEdit?.replaceA ?: "",
            initialReplaceB = itemToEdit?.replaceB ?: "",
            isEditMode = true,
            itemId = itemToEdit?.id
        )
    }

    // 删除确认对话框
    if (showDeleteDialog && itemToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteDialog = false
                itemToDelete = null
            },
            title = {
                Text(
                    text = "Delete Confirmation",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to delete \"${itemToDelete?.replaceA} - ${itemToDelete?.replaceB}\"? This action cannot be undone.",
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        itemToDelete?.let { deleteReplaceManagement(it) }
                        showDeleteDialog = false
                        itemToDelete = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFFF4444)
                    )
                ) {
                    Text("Delete", color = Color.White)
                }
            },
            dismissButton = {
                OutlinedButton(
                    onClick = {
                        showDeleteDialog = false
                        itemToDelete = null
                    }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}
