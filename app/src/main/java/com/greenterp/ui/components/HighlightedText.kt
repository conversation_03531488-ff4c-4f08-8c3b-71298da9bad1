package com.greenterp.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.greenterp.ProcessedText
import com.greenterp.TextHighlight
import kotlin.math.max
import kotlin.math.min

/**
 * 显示带词汇表高亮的文本组件，支持触控笔划词
 */
@Composable
fun HighlightedText(
    processedText: ProcessedText,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 16.sp,
    normalColor: Color = MaterialTheme.colorScheme.onSurface,
    highlightColor: Color = Color.Red,
    replaceHighlightColor: Color = Color.Black,
    onTextClick: ((Int) -> Unit)? = null,
    onStylusTextSelected: ((String) -> Unit)? = null // 新增：触控笔划词回调
) {
    // 划词状态管理
    var selectionStart by remember { mutableStateOf<Int?>(null) }
    var selectionEnd by remember { mutableStateOf<Int?>(null) }
    var textLayoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }

    val annotatedString = buildAnnotatedString {
        val text = processedText.text

        // 先处理替换表的**标记，再处理词汇表高亮
        processTextWithMarkersAndHighlights(text, processedText.highlights, normalColor, highlightColor)

        // 如果有选择区域，添加选择高亮
        if (selectionStart != null && selectionEnd != null) {
            val start = min(selectionStart!!, selectionEnd!!)
            val end = max(selectionStart!!, selectionEnd!!)
            if (start < end && end <= text.length) {
                addStyle(
                    style = SpanStyle(
                        background = Color(0x4407c160) // 半透明绿色背景
                    ),
                    start = start,
                    end = end
                )
            }
        }
    }

    // 计算动态行间距：字体大小 * 1.4 作为行间距
    val lineHeight = fontSize * 1.4f

    val textStyle = TextStyle(
        fontSize = fontSize,
        lineHeight = lineHeight
    )

    // 使用Box包装，添加触控笔手势检测
    Box(
        modifier = modifier
            .pointerInput(Unit) {
                if (onStylusTextSelected != null) {
                    awaitEachGesture {
                        val down = awaitFirstDown()

                        // 只处理触控笔输入
                        if (down.type == PointerType.Stylus) {
                            val startOffset = down.position
                            val startIndex = textLayoutResult?.getOffsetForPosition(startOffset) ?: 0
                            selectionStart = startIndex
                            selectionEnd = startIndex

                            // 跟踪拖动
                            do {
                                val event = awaitPointerEvent()
                                val change = event.changes.first()

                                if (change.pressed) {
                                    val currentOffset = change.position
                                    val currentIndex = textLayoutResult?.getOffsetForPosition(currentOffset) ?: 0
                                    selectionEnd = currentIndex
                                }
                            } while (event.changes.any { it.pressed })

                            // 拖动结束，提取选中文字
                            if (selectionStart != null && selectionEnd != null) {
                                val start = min(selectionStart!!, selectionEnd!!)
                                val end = max(selectionStart!!, selectionEnd!!)

                                if (start < end && end <= processedText.text.length) {
                                    val selectedText = processedText.text.substring(start, end).trim()
                                    if (selectedText.isNotEmpty()) {
                                        onStylusTextSelected(selectedText)
                                    }
                                }

                                // 清除选择状态
                                selectionStart = null
                                selectionEnd = null
                            }
                        }
                    }
                }
            }
    ) {
        if (onTextClick != null) {
            ClickableText(
                text = annotatedString,
                style = textStyle,
                onClick = onTextClick,
                onTextLayout = { layoutResult ->
                    textLayoutResult = layoutResult
                }
            )
        } else {
            androidx.compose.material3.Text(
                text = annotatedString,
                style = textStyle,
                onTextLayout = { layoutResult ->
                    textLayoutResult = layoutResult
                }
            )
        }
    }
}

/**
 * 简化版本，直接处理原始文本
 */
@Composable
fun HighlightedAsrText(
    text: String,
    highlights: List<TextHighlight>,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 16.sp,
    normalColor: Color = MaterialTheme.colorScheme.onSurface,
    highlightColor: Color = Color.Red
) {
    val processedText = ProcessedText(text, highlights)
    HighlightedText(
        processedText = processedText,
        modifier = modifier,
        fontSize = fontSize,
        normalColor = normalColor,
        highlightColor = highlightColor
    )
}

/**
 * 处理包含**标记的文本和词汇表高亮
 */
private fun androidx.compose.ui.text.AnnotatedString.Builder.processTextWithMarkersAndHighlights(
    text: String,
    highlights: List<TextHighlight>,
    normalColor: Color,
    highlightColor: Color
) {
    // 1. 先找到所有**标记的位置
    val replaceMarkers = mutableListOf<Pair<IntRange, String>>()
    val replaceRegex = Regex("""\*\*(.*?)\*\*""")
    replaceRegex.findAll(text).forEach { matchResult ->
        replaceMarkers.add(matchResult.range to matchResult.groupValues[1])
    }

    // 2. 创建一个映射，将原始位置映射到去除标记后的位置
    val cleanText = text.replace(replaceRegex, "$1")
    val positionMap = createPositionMap(text, cleanText)

    // 3. 构建最终的AnnotatedString
    var lastIndex = 0
    val allSegments = mutableListOf<TextSegment>()

    // 添加替换表标记段
    for ((range, replacedText) in replaceMarkers) {
        val cleanStart = positionMap[range.first] ?: range.first
        val cleanEnd = cleanStart + replacedText.length
        allSegments.add(TextSegment(cleanStart, cleanEnd, replacedText, SegmentType.REPLACE))
    }

    // 添加词汇表高亮段
    for (highlight in highlights) {
        if (highlight.isGlossaryA) {
            allSegments.add(TextSegment(
                highlight.start,
                highlight.end,
                cleanText.substring(highlight.start, highlight.end),
                SegmentType.GLOSSARY
            ))
        }
    }

    // 按位置排序并合并重叠
    val sortedSegments = allSegments.sortedBy { it.start }
    val mergedSegments = mergeOverlappingSegments(sortedSegments)

    // 构建AnnotatedString
    for (segment in mergedSegments) {
        // 添加段前的普通文本
        if (segment.start > lastIndex) {
            withStyle(style = SpanStyle(color = normalColor)) {
                append(cleanText.substring(lastIndex, segment.start))
            }
        }

        // 添加高亮段
        when (segment.type) {
            SegmentType.REPLACE -> {
                withStyle(style = SpanStyle(color = normalColor, fontWeight = FontWeight.Bold)) {
                    append(segment.text)
                }
            }
            SegmentType.GLOSSARY -> {
                withStyle(style = SpanStyle(color = highlightColor, fontWeight = FontWeight.Bold)) {
                    append(segment.text)
                }
            }
            SegmentType.BOTH -> {
                // 重叠区域，优先显示替换表样式
                withStyle(style = SpanStyle(color = normalColor, fontWeight = FontWeight.Bold)) {
                    append(segment.text)
                }
            }
        }

        lastIndex = segment.end
    }

    // 添加剩余的普通文本
    if (lastIndex < cleanText.length) {
        withStyle(style = SpanStyle(color = normalColor)) {
            append(cleanText.substring(lastIndex))
        }
    }
}

/**
 * 创建位置映射表，将包含**标记的位置映射到去除标记后的位置
 */
private fun createPositionMap(originalText: String, cleanText: String): Map<Int, Int> {
    val map = mutableMapOf<Int, Int>()
    var originalIndex = 0
    var cleanIndex = 0

    while (originalIndex < originalText.length) {
        if (originalIndex < originalText.length - 1 &&
            originalText.substring(originalIndex, originalIndex + 2) == "**") {
            // 跳过**标记
            originalIndex += 2
        } else {
            map[originalIndex] = cleanIndex
            originalIndex++
            cleanIndex++
        }
    }

    return map
}

/**
 * 文本段数据类
 */
private data class TextSegment(
    val start: Int,
    val end: Int,
    val text: String,
    val type: SegmentType
)

/**
 * 段类型枚举
 */
private enum class SegmentType {
    REPLACE,    // 替换表
    GLOSSARY,   // 词汇表
    BOTH        // 重叠区域
}

/**
 * 合并重叠的文本段
 */
private fun mergeOverlappingSegments(segments: List<TextSegment>): List<TextSegment> {
    if (segments.isEmpty()) return segments

    val result = mutableListOf<TextSegment>()
    var current = segments[0]

    for (i in 1 until segments.size) {
        val next = segments[i]

        if (current.end > next.start) {
            // 有重叠，合并
            val mergedStart = minOf(current.start, next.start)
            val mergedEnd = maxOf(current.end, next.end)
            val mergedText = current.text // 使用第一个段的文本
            val mergedType = if (current.type != next.type) SegmentType.BOTH else current.type

            current = TextSegment(mergedStart, mergedEnd, mergedText, mergedType)
        } else {
            // 无重叠，添加当前段并更新
            result.add(current)
            current = next
        }
    }

    result.add(current)
    return result
}
