package com.greenterp.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.greenterp.R

/**
 * 底部操作栏组件
 * 包含设置、词汇表、替换、翻译、清除、语言选择和开始/停止按钮
 */
@Composable
fun BottomActionBar(
    isRecording: Boolean = false,
    connectionState: String = "idle",
    primaryLanguage: String = "English-United States",
    secondaryLanguage: String = "Chinese-Mandarin",
    currentLanguageIsPrimary: Boolean = true,
    isTranslateActive: Boolean = false,
    currentLanguageDisplay: String? = null, // 新增：当前选中语言的显示名称
    onSettingsClick: () -> Unit = {},
    onDictionaryClick: () -> Unit = {},
    onReplaceClick: () -> Unit = {},
    onTranslateClick: () -> Unit = {},
    onStartStopClick: () -> Unit = {},
    onLanguageToggle: () -> Unit = {},
    isDarkTheme: Boolean = false
) {
    Surface(
        modifier = Modifier.fillMaxWidth()
            .navigationBarsPadding(),
        color = if (isDarkTheme) Color(0xFF2D2D2D) else Color(0xFFF5F5F5)
    ) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
            // 左侧功能按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 设置按钮
                IconButton(
                    onClick = onSettingsClick,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            if (isDarkTheme) Color(0xFF404040) else Color.White,
                            CircleShape
                        )
                        .border(
                            1.dp,
                            if (isDarkTheme) Color(0xFF555555) else Color(0xFFE0E0E0),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.Settings,
                        contentDescription = "Settings",
                        tint = Color(0xFF07c160),
                        modifier = Modifier.size(20.dp)
                    )
                }

                // 词汇表按钮
                IconButton(
                    onClick = onDictionaryClick,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            if (isDarkTheme) Color(0xFF404040) else Color.White,
                            CircleShape
                        )
                        .border(
                            1.dp,
                            if (isDarkTheme) Color(0xFF555555) else Color(0xFFE0E0E0),
                            CircleShape
                        )
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.dictionary_24px),
                        contentDescription = "Dictionary",
                        tint = Color(0xFF07c160),
                        modifier = Modifier.size(20.dp)
                    )
                }

                // 替换按钮
                IconButton(
                    onClick = onReplaceClick,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            Color.White,
                            CircleShape
                        )
                        .border(
                            1.dp,
                            Color(0xFFE0E0E0),
                            CircleShape
                        )
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.replace_24px),
                        contentDescription = "Replace",
                        tint = Color(0xFF07c160),
                        modifier = Modifier.size(20.dp)
                    )
                }

                // 文本翻译按钮
                IconButton(
                    onClick = onTranslateClick,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            if (isTranslateActive) Color(0xFF07c160) else (if (isDarkTheme) Color(0xFF404040) else Color.White),
                            CircleShape
                        )
                        .border(
                            1.dp,
                            if (isDarkTheme) Color(0xFF555555) else Color(0xFFE0E0E0),
                            CircleShape
                        )
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.translate_24px),
                        contentDescription = "Text Translate",
                        tint = if (isTranslateActive) Color.White else Color(0xFF07c160),
                        modifier = Modifier.size(20.dp)
                    )
                }

                // 清除按钮
                IconButton(
                    onClick = { /* TODO: 清除功能 */ },
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            if (isDarkTheme) Color(0xFF404040) else Color.White,
                            CircleShape
                        )
                        .border(
                            1.dp,
                            if (isDarkTheme) Color(0xFF555555) else Color(0xFFE0E0E0),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "Clear",
                        tint = Color(0xFF07c160),
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            // 语言选择按钮
            Button(
                onClick = onLanguageToggle,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF07c160)
                ),
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier.height(40.dp)
            ) {
                // 优先使用新的语言显示系统，否则回退到旧系统
                val displayLanguage = currentLanguageDisplay
                    ?: (if (currentLanguageIsPrimary) primaryLanguage else secondaryLanguage)
                val displayText = displayLanguage.replace("-", " - ")
                Text(
                    text = displayText,
                    color = Color.White,
                    fontSize = 14.sp
                )
            }

            // 开始/停止按钮 - 紧挨着语言选择按钮
            Button(
                onClick = onStartStopClick,
                enabled = connectionState != "connecting", // 连接中时禁用按钮
                colors = ButtonDefaults.buttonColors(
                    containerColor = when {
                        isRecording -> Color(0xFFFF4444) // 录音中：红色
                        connectionState == "connecting" -> Color(0xFFFFB74D) // 连接中：橙色
                        connectionState == "error" -> Color(0xFFFF7043) // 错误：橙红色
                        else -> Color(0xFF07c160) // 空闲：绿色
                    },
                    disabledContainerColor = Color(0xFFFFB74D) // 禁用时的颜色
                ),
                shape = RoundedCornerShape(8.dp),
                elevation = ButtonDefaults.buttonElevation(
                    defaultElevation = 0.dp,
                    pressedElevation = 0.dp,
                    focusedElevation = 0.dp,
                    hoveredElevation = 0.dp,
                    disabledElevation = 0.dp
                ),
                modifier = Modifier.height(40.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 连接中时显示加载指示器
                    if (connectionState == "connecting") {
                        androidx.compose.material3.CircularProgressIndicator(
                            modifier = Modifier.size(12.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    }

                    Text(
                        text = when {
                            isRecording -> "■ Stop"
                            connectionState == "connecting" -> "Connecting..."
                            connectionState == "error" -> "▶ Retry"
                            else -> "▶ Start"
                        },
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f))
        }
        }
    }
}
