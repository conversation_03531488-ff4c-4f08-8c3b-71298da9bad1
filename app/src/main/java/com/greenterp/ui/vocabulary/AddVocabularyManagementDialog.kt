package com.greenterp.ui.vocabulary

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import android.widget.Toast
import com.greenterp.data.VocabularyManagementSaveResult
import com.greenterp.network.ApiService
import kotlinx.coroutines.launch

/**
 * 添加/编辑词汇管理对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddVocabularyManagementDialog(
    sceneId: Int,
    onDismiss: () -> Unit,
    onSaveSuccess: () -> Unit,
    initialGlossaryA: String = "", // 用于编辑模式的初始A值
    initialGlossaryB: String = "", // 用于编辑模式的初始B值
    isEditMode: Boolean = false, // 是否为编辑模式
    itemId: Int? = null // 编辑模式下的项目ID
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }

    var glossaryA by remember { mutableStateOf(initialGlossaryA) }
    var glossaryB by remember { mutableStateOf(initialGlossaryB) }
    var saveResult by remember { mutableStateOf<VocabularyManagementSaveResult?>(null) }
    var isGlossaryAError by remember { mutableStateOf(false) }
    var isGlossaryBError by remember { mutableStateOf(false) }

    // 保存词汇
    fun saveVocabulary() {
        // 验证输入
        isGlossaryAError = glossaryA.isBlank()
        isGlossaryBError = glossaryB.isBlank()

        if (isGlossaryAError || isGlossaryBError) {
            return
        }

        coroutineScope.launch {
            saveResult = VocabularyManagementSaveResult.Loading
            // 实时获取userId，确保使用当前登录用户的数据
            val userId = apiService.getCurrentUserId()
            val result = apiService.saveVocabularyManagement(glossaryA, glossaryB, sceneId, userId, if (isEditMode) itemId else null)
            saveResult = result

            when (result) {
                is VocabularyManagementSaveResult.Success -> {
                    val message = if (isEditMode) "Edit successful" else "Add successful"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    onSaveSuccess()
                    onDismiss()
                }
                is VocabularyManagementSaveResult.Error -> {
                    val message = if (isEditMode) "Edit failed: ${result.message}" else "Add failed: ${result.message}"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                }
                else -> {}
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            modifier = Modifier
                .width(400.dp)
                .wrapContentHeight(),
            shape = RoundedCornerShape(12.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = "Add Vocabulary",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // A字段输入框
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(bottom = 8.dp)
                    ) {
                        Text(
                            text = "*",
                            color = Color.Red,
                            fontSize = 14.sp,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        Text(
                            text = "A",
                            fontSize = 14.sp,
                            color = Color.Black
                        )
                    }
                    
                    OutlinedTextField(
                        value = glossaryA,
                        onValueChange = { 
                            glossaryA = it
                            isGlossaryAError = false
                        },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = if (isGlossaryAError) Color.Red else Color(0xFFE0E0E0),
                            unfocusedBorderColor = if (isGlossaryAError) Color.Red else Color(0xFFE0E0E0)
                        ),
                        isError = isGlossaryAError
                    )
                    
                    if (isGlossaryAError) {
                        Text(
                            text = "A field is required",
                            color = Color.Red,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // B字段输入框
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(bottom = 8.dp)
                    ) {
                        Text(
                            text = "*",
                            color = Color.Red,
                            fontSize = 14.sp,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        Text(
                            text = "B",
                            fontSize = 14.sp,
                            color = Color.Black
                        )
                    }
                    
                    OutlinedTextField(
                        value = glossaryB,
                        onValueChange = { 
                            glossaryB = it
                            isGlossaryBError = false
                        },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = if (isGlossaryBError) Color.Red else Color(0xFFE0E0E0),
                            unfocusedBorderColor = if (isGlossaryBError) Color.Red else Color(0xFFE0E0E0)
                        ),
                        isError = isGlossaryBError
                    )
                    
                    if (isGlossaryBError) {
                        Text(
                            text = "B field is required",
                            color = Color.Red,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Cancel 按钮
                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color.Black
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier
                            .weight(1f)
                            .height(40.dp)
                    ) {
                        Text("Cancel", fontSize = 14.sp)
                    }
                    
                    // Save 按钮
                    Button(
                        onClick = { saveVocabulary() },
                        enabled = saveResult !is VocabularyManagementSaveResult.Loading,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier
                            .weight(1f)
                            .height(40.dp)
                    ) {
                        if (saveResult is VocabularyManagementSaveResult.Loading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text("Save", fontSize = 14.sp)
                        }
                    }
                }
            }
        }
    }
}
