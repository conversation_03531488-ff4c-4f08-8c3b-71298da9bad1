package com.greenterp.ui.vocabulary

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import android.content.Intent
import android.net.Uri
import android.os.Environment
import androidx.core.content.FileProvider
import com.greenterp.data.VocabularyManagementListResult
import com.greenterp.data.VocabularyManagementDeleteResult
import com.greenterp.data.VocabularyManagementExportResult
import com.greenterp.data.VocabularyManagementImportResult
import com.greenterp.network.ApiService
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream

/**
 * 词汇管理对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VocabularyManagementDialog(
    vocabularyName: String,
    sceneId: Int = 90, // 默认场景ID，实际应该从上级传入
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }

    var searchText by remember { mutableStateOf("") }
    var vocabularyManagementResult by remember { mutableStateOf<VocabularyManagementListResult>(VocabularyManagementListResult.Loading) }
    var currentPage by remember { mutableStateOf(0) }
    var totalCount by remember { mutableStateOf(0) }
    var showAddDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }
    var itemToEdit by remember { mutableStateOf<com.greenterp.data.VocabularyManagementData?>(null) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var itemToDelete by remember { mutableStateOf<com.greenterp.data.VocabularyManagementData?>(null) }
    var deleteResult by remember { mutableStateOf<VocabularyManagementDeleteResult?>(null) }
    var exportResult by remember { mutableStateOf<VocabularyManagementExportResult?>(null) }
    var importResult by remember { mutableStateOf<VocabularyManagementImportResult?>(null) }

    val pageSize = 10

    // 文件保存launcher
    val saveFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("application/vnd.ms-excel")
    ) { uri ->
        uri?.let { fileUri ->
            exportResult?.let { result ->
                if (result is VocabularyManagementExportResult.Success) {
                    try {
                        context.contentResolver.openOutputStream(fileUri)?.use { outputStream ->
                            outputStream.write(result.fileBytes)
                            outputStream.flush()
                            Toast.makeText(context, "File saved successfully", Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Toast.makeText(context, "Failed to save file: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }

    // 执行搜索的函数
    fun performSearch() {
        coroutineScope.launch {
            vocabularyManagementResult = VocabularyManagementListResult.Loading
            val searchInfo = if (searchText.isNotBlank()) searchText else null
            // 实时获取userId，确保使用当前登录用户的数据
            val userId = apiService.getCurrentUserId()
            vocabularyManagementResult = apiService.getVocabularyManagementList(currentPage, pageSize, sceneId, userId, searchInfo)
            if (vocabularyManagementResult is VocabularyManagementListResult.Success) {
                totalCount = (vocabularyManagementResult as VocabularyManagementListResult.Success).vocabularyList.count
            }
        }
    }

    // 文件选择launcher
    val selectFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { fileUri ->
            try {
                // 创建临时文件
                val inputStream = context.contentResolver.openInputStream(fileUri)
                val tempFile = File(context.cacheDir, "temp_upload.xls")

                inputStream?.use { input ->
                    tempFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }

                // 上传文件
                coroutineScope.launch {
                    importResult = VocabularyManagementImportResult.Loading
                    // 实时获取userId，确保使用当前登录用户的数据
                    val userId = apiService.getCurrentUserId()
                    val result = apiService.importVocabularyManagement(tempFile, sceneId, userId)
                    importResult = result

                    when (result) {
                        is VocabularyManagementImportResult.Success -> {
                            Toast.makeText(context, "Import successful", Toast.LENGTH_SHORT).show()
                            // 刷新列表
                            performSearch()
                        }
                        is VocabularyManagementImportResult.Error -> {
                            Toast.makeText(context, "Import failed: ${result.message}", Toast.LENGTH_SHORT).show()
                        }
                        else -> {}
                    }

                    // 清理临时文件
                    tempFile.delete()
                }
            } catch (e: Exception) {
                Toast.makeText(context, "Failed to read file: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }


    // 页码变化时重新搜索（包括初始加载）
    LaunchedEffect(currentPage) {
        performSearch()
    }

    // 搜索文本变化时的防抖搜索
    LaunchedEffect(searchText) {
        if (searchText.isNotEmpty()) {
            delay(500) // 防抖延迟500ms
            if (currentPage != 0) {
                currentPage = 0 // 重置到第一页，这会触发上面的LaunchedEffect
            } else {
                performSearch() // 如果已经在第一页，直接搜索
            }
        } else {
            // 搜索框清空时立即搜索
            if (currentPage != 0) {
                currentPage = 0 // 重置到第一页，这会触发上面的LaunchedEffect
            } else {
                performSearch() // 如果已经在第一页，直接搜索
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 顶部区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back 按钮
                    TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF07c160)
                        )
                    ) {
                        Icon(
                            Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Back", fontSize = 14.sp)
                    }
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 标题
                    Text(
                        text = "Vocabulary($vocabularyName)",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                }

                // 搜索和操作区域 - 所有元素在同一行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 搜索输入框 - 固定宽度
                    OutlinedTextField(
                        value = searchText,
                        onValueChange = { searchText = it },
                        placeholder = { Text("Search Info", fontSize = 14.sp) },
                        modifier = Modifier.width(200.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFFE0E0E0),
                            unfocusedBorderColor = Color(0xFFE0E0E0)
                        )
                    )

                    // Search 按钮
                    Button(
                        onClick = {
                            // 重置到第一页并执行搜索
                            currentPage = 0
                            performSearch()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color.Black
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Search", fontSize = 14.sp)
                    }

                    // Add 按钮
                    Button(
                        onClick = {
                            showAddDialog = true
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Add", fontSize = 14.sp, color = Color.White)
                    }

                    // Upload 按钮
                    Button(
                        onClick = {
                            selectFileLauncher.launch("application/vnd.ms-excel")
                        },
                        enabled = importResult !is VocabularyManagementImportResult.Loading,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        if (importResult is VocabularyManagementImportResult.Loading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text("Upload", fontSize = 14.sp, color = Color.White)
                        }
                    }

                    // Download 按钮
                    Button(
                        onClick = {
                            coroutineScope.launch {
                                exportResult = VocabularyManagementExportResult.Loading
                                // 实时获取userId，确保使用当前登录用户的数据
                                val userId = apiService.getCurrentUserId()
                                val result = apiService.exportVocabularyManagement(sceneId, userId)
                                exportResult = result

                                when (result) {
                                    is VocabularyManagementExportResult.Success -> {
                                        // 启动文件保存对话框
                                        saveFileLauncher.launch("Vocabulary.xls")
                                    }
                                    is VocabularyManagementExportResult.Error -> {
                                        Toast.makeText(context, "Export failed: ${result.message}", Toast.LENGTH_SHORT).show()
                                    }
                                    else -> {}
                                }
                            }
                        },
                        enabled = exportResult !is VocabularyManagementExportResult.Loading,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        if (exportResult is VocabularyManagementExportResult.Loading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text("Download", fontSize = 14.sp, color = Color.White)
                        }
                    }
                }

                // 表格区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    // 表头
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xFFF8F9FA))
                            .border(1.dp, Color(0xFFE0E0E0))
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Index",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(1f)
                        )
                        Row(
                            modifier = Modifier.weight(2f),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Primary",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Icon(
                                Icons.Default.KeyboardArrowDown,
                                contentDescription = "Sort",
                                modifier = Modifier.size(16.dp),
                                tint = Color.Gray
                            )
                        }
                        Text(
                            text = "Secondary",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(2f)
                        )
                        Row(
                            modifier = Modifier.weight(2f),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Create Date",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Icon(
                                Icons.Default.KeyboardArrowDown,
                                contentDescription = "Sort",
                                modifier = Modifier.size(16.dp),
                                tint = Color.Gray
                            )
                        }
                        Text(
                            text = "Operate",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(2f),
                            textAlign = TextAlign.Center
                        )
                    }

                    // 表格数据行
                    when (val currentResult = vocabularyManagementResult) {
                        is VocabularyManagementListResult.Loading -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(color = Color(0xFF07c160))
                            }
                        }
                        is VocabularyManagementListResult.Error -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "Error: ${currentResult.message}",
                                    color = Color.Red,
                                    fontSize = 14.sp
                                )
                            }
                        }
                        is VocabularyManagementListResult.Success -> {
                            LazyColumn {
                                itemsIndexed(currentResult.vocabularyList.data) { index, item ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .border(1.dp, Color(0xFFE0E0E0))
                                            .padding(12.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "${currentPage * pageSize + index + 1}",
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = item.glossaryA,
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(2f)
                                        )
                                        Text(
                                            text = item.glossaryB,
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(2f)
                                        )
                                        Text(
                                            text = item.createAt,
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(2f)
                                        )

                                        // 操作按钮
                                        Row(
                                            modifier = Modifier.weight(2f),
                                            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally)
                                        ) {
                                            Button(
                                                onClick = {
                                                    itemToEdit = item
                                                    showEditDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFF5F5F5),
                                                    contentColor = Color.Black
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Edit", fontSize = 12.sp)
                                            }

                                            Button(
                                                onClick = {
                                                    itemToDelete = item
                                                    showDeleteDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFE53E3E),
                                                    contentColor = Color.White
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Delete", fontSize = 12.sp)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 分页控件
                if (vocabularyManagementResult is VocabularyManagementListResult.Success) {
                    val totalPages = kotlin.math.ceil(totalCount.toDouble() / pageSize).toInt()

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 16.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 上一页箭头
                        IconButton(
                            onClick = { if (currentPage > 0) currentPage-- },
                            enabled = currentPage > 0,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Text(
                                text = "‹",
                                fontSize = 18.sp,
                                color = if (currentPage > 0) Color(0xFF07c160) else Color.Gray
                            )
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        // 当前页码
                        Text(
                            text = "${currentPage + 1}",
                            fontSize = 16.sp,
                            color = Color(0xFF07c160),
                            fontWeight = FontWeight.Medium
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        // 下一页箭头
                        IconButton(
                            onClick = { if (currentPage < totalPages - 1) currentPage++ },
                            enabled = currentPage < totalPages - 1,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Text(
                                text = "›",
                                fontSize = 18.sp,
                                color = if (currentPage < totalPages - 1) Color(0xFF07c160) else Color.Gray
                            )
                        }
                    }
                }
            }
        }
    }

    // 添加词汇对话框
    if (showAddDialog) {
        AddVocabularyManagementDialog(
            sceneId = sceneId,
            onDismiss = { showAddDialog = false },
            onSaveSuccess = {
                // 保存成功后刷新列表
                performSearch()
            }
        )
    }

    // 编辑词汇对话框
    if (showEditDialog && itemToEdit != null) {
        AddVocabularyManagementDialog(
            sceneId = sceneId,
            onDismiss = {
                showEditDialog = false
                itemToEdit = null
            },
            onSaveSuccess = {
                // 保存成功后刷新列表
                performSearch()
            },
            initialGlossaryA = itemToEdit?.glossaryA ?: "",
            initialGlossaryB = itemToEdit?.glossaryB ?: "",
            isEditMode = true,
            itemId = itemToEdit?.id
        )
    }

    // 删除确认对话框
    if (showDeleteDialog && itemToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteDialog = false
                itemToDelete = null
            },
            title = {
                Text(
                    text = "Delete Confirmation",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to delete \"${itemToDelete?.glossaryA} - ${itemToDelete?.glossaryB}\"? This action cannot be undone.",
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        itemToDelete?.let { item ->
                            coroutineScope.launch {
                                deleteResult = VocabularyManagementDeleteResult.Loading
                                // 实时获取userId，确保使用当前登录用户的数据
                                val userId = apiService.getCurrentUserId()
                                val result = apiService.deleteVocabularyManagement(listOf(item.id), userId)
                                deleteResult = result

                                when (result) {
                                    is VocabularyManagementDeleteResult.Success -> {
                                        Toast.makeText(context, "Delete successful", Toast.LENGTH_SHORT).show()
                                        // 刷新列表
                                        performSearch()
                                    }
                                    is VocabularyManagementDeleteResult.Error -> {
                                        Toast.makeText(context, "Delete failed: ${result.message}", Toast.LENGTH_SHORT).show()
                                    }
                                    else -> {}
                                }

                                showDeleteDialog = false
                                itemToDelete = null
                            }
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFE53E3E)
                    ),
                    enabled = deleteResult !is VocabularyManagementDeleteResult.Loading
                ) {
                    if (deleteResult is VocabularyManagementDeleteResult.Loading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text("Delete", color = Color.White)
                    }
                }
            },
            dismissButton = {
                Button(
                    onClick = {
                        showDeleteDialog = false
                        itemToDelete = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFF5F5F5),
                        contentColor = Color.Black
                    )
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}
