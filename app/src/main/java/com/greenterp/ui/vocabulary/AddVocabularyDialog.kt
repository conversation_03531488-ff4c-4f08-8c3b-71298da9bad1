package com.greenterp.ui.vocabulary

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import android.widget.Toast
import com.greenterp.data.VocabularySaveResult
import com.greenterp.network.ApiService
import kotlinx.coroutines.launch

/**
 * 添加/编辑词汇表对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddVocabularyDialog(
    onDismiss: () -> Unit,
    onSaveSuccess: () -> Unit,
    initialName: String = "", // 用于编辑模式的初始名称
    isEditMode: Boolean = false, // 是否为编辑模式
    itemId: Int? = null // 编辑模式下的项目ID
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }

    var vocabularyName by remember { mutableStateOf(initialName) }
    var saveResult by remember { mutableStateOf<VocabularySaveResult?>(null) }
    var isNameError by remember { mutableStateOf(false) }

    // 保存词汇表
    fun saveVocabulary() {
        if (vocabularyName.isBlank()) {
            isNameError = true
            return
        }

        isNameError = false
        coroutineScope.launch {
            saveResult = VocabularySaveResult.Loading
            // 实时获取userId，确保使用当前登录用户的数据
            val userId = apiService.getCurrentUserId()
            val result = if (isEditMode && itemId != null) {
                // 编辑模式：调用更新接口
                apiService.updateVocabulary(itemId, vocabularyName, userId)
            } else {
                // 添加模式：调用保存接口
                apiService.saveVocabulary(vocabularyName, userId)
            }
            saveResult = result

            when (result) {
                is VocabularySaveResult.Success -> {
                    val message = if (isEditMode) "Edit successful" else "Add successful"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    onSaveSuccess()
                    onDismiss()
                }
                is VocabularySaveResult.Error -> {
                    val message = if (isEditMode) "Edit failed: ${result.message}" else "Add failed: ${result.message}"
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                }
                else -> {}
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            modifier = Modifier
                .width(400.dp)
                .wrapContentHeight(),
            shape = RoundedCornerShape(12.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = if (isEditMode) "Edit Vocabulary Scene" else "Add Vocabulary Scene",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // 名称输入框
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(bottom = 8.dp)
                    ) {
                        Text(
                            text = "*",
                            color = Color.Red,
                            fontSize = 14.sp
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "Name",
                            fontSize = 14.sp,
                            color = Color.Black
                        )
                    }
                    
                    OutlinedTextField(
                        value = vocabularyName,
                        onValueChange = { 
                            vocabularyName = it
                            isNameError = false
                        },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = if (isNameError) Color.Red else Color(0xFFE0E0E0),
                            unfocusedBorderColor = if (isNameError) Color.Red else Color(0xFFE0E0E0),
                            errorBorderColor = Color.Red
                        ),
                        isError = isNameError,
                        singleLine = true
                    )
                    
                    if (isNameError) {
                        Text(
                            text = "Name is required",
                            color = Color.Red,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 保存结果显示 - 只显示加载状态
                when (val result = saveResult) {
                    is VocabularySaveResult.Loading -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(bottom = 16.dp)
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color(0xFF07c160),
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = if (isEditMode) "Updating..." else "Saving...",
                                fontSize = 14.sp,
                                color = Color.Gray
                            )
                        }
                    }
                    else -> {
                        // 其他状态不显示，使用Toast提示
                        Spacer(modifier = Modifier.height(0.dp))
                    }
                }

                // 按钮区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Cancel 按钮
                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color.Black
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier
                            .weight(1f)
                            .height(40.dp)
                    ) {
                        Text("Cancel", fontSize = 14.sp)
                    }
                    
                    // Save 按钮
                    Button(
                        onClick = { saveVocabulary() },
                        enabled = saveResult !is VocabularySaveResult.Loading,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier
                            .weight(1f)
                            .height(40.dp)
                    ) {
                        Text(if (isEditMode) "Update" else "Save", fontSize = 14.sp)
                    }
                }
            }
        }
    }
}
