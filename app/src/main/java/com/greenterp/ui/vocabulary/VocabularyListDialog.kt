package com.greenterp.ui.vocabulary

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import android.widget.Toast
import com.greenterp.data.VocabularyData
import com.greenterp.data.VocabularyListResult
import com.greenterp.data.VocabularyListAllResult
import com.greenterp.data.VocabularyDeleteResult
import com.greenterp.network.ApiService
import com.greenterp.utils.SettingsManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * 词汇表全屏对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VocabularyListDialog(
    onDismiss: () -> Unit,
    onVocabularySelected: (List<Int>) -> Unit = {} // 新增：选择词汇表时的回调
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }
    val settingsManager = remember { SettingsManager(context) }

    var searchText by remember { mutableStateOf("") }
    // 从设置中恢复选择的词汇表
    var selectedList by remember { mutableStateOf(settingsManager.selectedVocabularyName) }
    var isDropdownExpanded by remember { mutableStateOf(false) }
    var vocabularyListResult by remember { mutableStateOf<VocabularyListResult>(VocabularyListResult.Loading) }
    var currentPage by remember { mutableStateOf(0) }
    var totalCount by remember { mutableStateOf(0) }
    var showAddDialog by remember { mutableStateOf(false) }
    var allVocabularyResult by remember { mutableStateOf<VocabularyListAllResult>(VocabularyListAllResult.Loading) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var itemToDelete by remember { mutableStateOf<VocabularyData?>(null) }
    var deleteResult by remember { mutableStateOf<VocabularyDeleteResult?>(null) }
    var showEditDialog by remember { mutableStateOf(false) }
    var itemToEdit by remember { mutableStateOf<VocabularyData?>(null) }
    var showManagementDialog by remember { mutableStateOf(false) }
    var itemToManage by remember { mutableStateOf<VocabularyData?>(null) }

    val currentAllVocabularyResult = allVocabularyResult
    val listOptions = when (currentAllVocabularyResult) {
        is VocabularyListAllResult.Success -> {
            val vocabularyNames = currentAllVocabularyResult.vocabularyList.map { it.name }
            // 添加"清空选择"选项
            listOf("None") + vocabularyNames
        }
        else -> emptyList()
    }
    val pageSize = 6

    // 执行搜索的函数
    fun performSearch() {
        coroutineScope.launch {
            vocabularyListResult = VocabularyListResult.Loading
            val searchInfo = if (searchText.isNotBlank()) searchText else null
            // 每次搜索时实时获取userId，确保使用当前登录用户的数据
            val userId = apiService.getCurrentUserId()
            vocabularyListResult = apiService.getVocabularyList(currentPage, pageSize, userId, searchInfo)
            if (vocabularyListResult is VocabularyListResult.Success) {
                totalCount = (vocabularyListResult as VocabularyListResult.Success).vocabularyList.count
            }
        }
    }

    // 初始加载所有词汇表（用于下拉框）
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            // 实时获取userId，确保使用当前登录用户的数据
            val userId = apiService.getCurrentUserId()
            allVocabularyResult = apiService.getAllVocabularyList(userId)

            // 如果有保存的词汇表选择，自动加载对应的词汇数据
            if (settingsManager.selectedVocabularyId != -1) {
                onVocabularySelected(listOf(settingsManager.selectedVocabularyId))
                println("自动加载保存的词汇表: ${settingsManager.selectedVocabularyName}, ID: ${settingsManager.selectedVocabularyId}")
            }
        }
    }

    // 页码变化时重新搜索（包括初始加载）
    LaunchedEffect(currentPage) {
        performSearch()
    }

    // 搜索文本变化时的防抖搜索
    LaunchedEffect(searchText) {
        if (searchText.isNotEmpty()) {
            delay(500) // 防抖延迟500ms
            if (currentPage != 0) {
                currentPage = 0 // 重置到第一页，这会触发上面的LaunchedEffect
            } else {
                performSearch() // 如果已经在第一页，直接搜索
            }
        } else {
            // 搜索框清空时立即搜索
            if (currentPage != 0) {
                currentPage = 0 // 重置到第一页，这会触发上面的LaunchedEffect
            } else {
                performSearch() // 如果已经在第一页，直接搜索
            }
        }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 顶部区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back 按钮
                    TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF07c160)
                        )
                    ) {
                        Icon(
                            Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Back", fontSize = 14.sp)
                    }
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 标题
                    Text(
                        text = "Vocabulary List",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                }

                // 搜索和操作区域 - 所有元素在同一行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 搜索输入框 - 固定宽度
                    OutlinedTextField(
                        value = searchText,
                        onValueChange = { searchText = it },
                        placeholder = { Text("Search Info", fontSize = 14.sp) },
                        modifier = Modifier.width(200.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFFE0E0E0),
                            unfocusedBorderColor = Color(0xFFE0E0E0)
                        )
                    )

                    // Search 按钮
                    Button(
                        onClick = {
                            // 重置到第一页并执行搜索
                            currentPage = 0
                            performSearch()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color.Black
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Search", fontSize = 14.sp)
                    }

                    // Add 按钮
                    Button(
                        onClick = { showAddDialog = true },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(56.dp)
                    ) {
                        Text("Add", fontSize = 14.sp, color = Color.White)
                    }

                    // Choose List 标签
                    Text(
                        text = "Choose List:",
                        fontSize = 14.sp,
                        modifier = Modifier.padding(start = 16.dp, end = 8.dp)
                    )

                    // Choose List 下拉框
                    ExposedDropdownMenuBox(
                        expanded = isDropdownExpanded,
                        onExpandedChange = { isDropdownExpanded = it },
                        modifier = Modifier.width(300.dp) // 调整宽度为原来的两倍
                    ) {
                        OutlinedTextField(
                            value = selectedList.ifEmpty { "Select" },
                            onValueChange = {},
                            readOnly = true,
                            trailingIcon = {
                                Icon(
                                    Icons.Default.ArrowDropDown,
                                    contentDescription = "Dropdown"
                                )
                            },
                            modifier = Modifier
                                .menuAnchor()
                                .fillMaxWidth(),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFFE0E0E0),
                                unfocusedBorderColor = Color(0xFFE0E0E0)
                            )
                        )

                        ExposedDropdownMenu(
                            expanded = isDropdownExpanded,
                            onDismissRequest = { isDropdownExpanded = false }
                        ) {
                            if (listOptions.isEmpty()) {
                                DropdownMenuItem(
                                    text = {
                                        Text(
                                            "Loading...",
                                            fontSize = 14.sp,
                                            color = Color.Gray
                                        )
                                    },
                                    onClick = { }
                                )
                            } else {
                                listOptions.forEach { option ->
                                    DropdownMenuItem(
                                        text = { Text(option, fontSize = 14.sp) },
                                        onClick = {
                                            selectedList = option
                                            isDropdownExpanded = false

                                            if (option == "None") {
                                                // 清空选择
                                                settingsManager.selectedVocabularyName = ""
                                                settingsManager.selectedVocabularyId = -1

                                                // 调用回调，传递空列表，这会清空词汇表数据
                                                onVocabularySelected(emptyList())
                                                println("清空词汇表选择")
                                            } else {
                                                // 获取选中词汇表的ID并调用回调
                                                when (currentAllVocabularyResult) {
                                                    is VocabularyListAllResult.Success -> {
                                                        val selectedVocabulary = currentAllVocabularyResult.vocabularyList.find { it.name == option }
                                                        selectedVocabulary?.let { vocab ->
                                                            // 保存选择到设置中
                                                            settingsManager.selectedVocabularyName = vocab.name
                                                            settingsManager.selectedVocabularyId = vocab.id

                                                            // 调用回调，传递选中的词汇表ID
                                                            onVocabularySelected(listOf(vocab.id))
                                                            println("选择词汇表: ${vocab.name}, ID: ${vocab.id}")
                                                        }
                                                    }
                                                    else -> {}
                                                }
                                            }
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                // 表格区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    // 表头
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xFFF8F9FA))
                            .border(1.dp, Color(0xFFE0E0E0))
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Index",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(1f)
                        )
                        Text(
                            text = "Name",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(2f)
                        )
                        Text(
                            text = "Operate",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(3f),
                            textAlign = TextAlign.Center
                        )
                    }

                    // 表格内容
                    val currentResult = vocabularyListResult
                    when (currentResult) {
                        is VocabularyListResult.Loading -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    color = Color(0xFF07c160)
                                )
                            }
                        }
                        is VocabularyListResult.Error -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "加载失败: ${currentResult.message}",
                                    color = Color.Red,
                                    fontSize = 14.sp
                                )
                            }
                        }
                        is VocabularyListResult.Success -> {
                            LazyColumn {
                                itemsIndexed(currentResult.vocabularyList.data) { index, item ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .border(
                                                width = 1.dp,
                                                color = Color(0xFFE0E0E0)
                                            )
                                            .padding(12.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = (currentPage * pageSize + index + 1).toString(),
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = item.name,
                                            fontSize = 14.sp,
                                            modifier = Modifier.weight(2f)
                                        )

                                        // 操作按钮
                                        Row(
                                            modifier = Modifier.weight(3f),
                                            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally)
                                        ) {
                                            Button(
                                                onClick = {
                                                    itemToManage = item
                                                    showManagementDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFF5F5F5),
                                                    contentColor = Color.Black
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Management", fontSize = 12.sp)
                                            }

                                            Button(
                                                onClick = {
                                                    itemToEdit = item
                                                    showEditDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFF5F5F5),
                                                    contentColor = Color.Black
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Edit", fontSize = 12.sp)
                                            }

                                            Button(
                                                onClick = {
                                                    itemToDelete = item
                                                    showDeleteDialog = true
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFFF4444),
                                                    contentColor = Color.White
                                                ),
                                                shape = RoundedCornerShape(4.dp),
                                                modifier = Modifier.height(32.dp)
                                            ) {
                                                Text("Delete", fontSize = 12.sp)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 底部分页器
                val currentResult = vocabularyListResult
                if (currentResult is VocabularyListResult.Success) {
                    val totalPages = kotlin.math.ceil(totalCount.toDouble() / pageSize).toInt()

                    if (totalPages > 1) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp, bottom = 24.dp),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 上一页箭头
                            IconButton(
                                onClick = { if (currentPage > 0) currentPage-- },
                                enabled = currentPage > 0,
                                modifier = Modifier.size(32.dp)
                            ) {
                                Text(
                                    text = "‹",
                                    fontSize = 18.sp,
                                    color = if (currentPage > 0) Color(0xFF07c160) else Color.Gray
                                )
                            }

                            Spacer(modifier = Modifier.width(8.dp))

                            // 页码按钮
                            for (page in 0 until totalPages) {
                                val isCurrentPage = page == currentPage

                                TextButton(
                                    onClick = { currentPage = page },
                                    colors = ButtonDefaults.textButtonColors(
                                        contentColor = if (isCurrentPage) Color.White else Color(0xFF07c160),
                                        containerColor = if (isCurrentPage) Color(0xFF07c160) else Color.Transparent
                                    ),
                                    shape = RoundedCornerShape(4.dp),
                                    modifier = Modifier
                                        .size(32.dp)
                                        .then(
                                            if (isCurrentPage) {
                                                Modifier.background(
                                                    Color(0xFF07c160),
                                                    RoundedCornerShape(4.dp)
                                                )
                                            } else {
                                                Modifier
                                            }
                                        ),
                                    contentPadding = PaddingValues(0.dp)
                                ) {
                                    Text(
                                        text = (page + 1).toString(),
                                        fontSize = 14.sp,
                                        fontWeight = if (isCurrentPage) FontWeight.Medium else FontWeight.Normal
                                    )
                                }

                                if (page < totalPages - 1) {
                                    Spacer(modifier = Modifier.width(4.dp))
                                }
                            }

                            Spacer(modifier = Modifier.width(8.dp))

                            // 下一页箭头
                            IconButton(
                                onClick = { if (currentPage < totalPages - 1) currentPage++ },
                                enabled = currentPage < totalPages - 1,
                                modifier = Modifier.size(32.dp)
                            ) {
                                Text(
                                    text = "›",
                                    fontSize = 18.sp,
                                    color = if (currentPage < totalPages - 1) Color(0xFF07c160) else Color.Gray
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // 添加词汇表对话框
    if (showAddDialog) {
        AddVocabularyDialog(
            onDismiss = { showAddDialog = false },
            onSaveSuccess = {
                // 保存成功后刷新列表和下拉框数据
                performSearch()
                coroutineScope.launch {
                    // 实时获取userId，确保使用当前登录用户的数据
                    val userId = apiService.getCurrentUserId()
                    allVocabularyResult = apiService.getAllVocabularyList(userId)
                }
            }
        )
    }

    // 编辑词汇表对话框
    if (showEditDialog && itemToEdit != null) {
        AddVocabularyDialog(
            onDismiss = {
                showEditDialog = false
                itemToEdit = null
            },
            onSaveSuccess = {
                // 保存成功后刷新列表和下拉框数据
                performSearch()
                coroutineScope.launch {
                    // 实时获取userId，确保使用当前登录用户的数据
                    val userId = apiService.getCurrentUserId()
                    allVocabularyResult = apiService.getAllVocabularyList(userId)
                }
            },
            initialName = itemToEdit?.name ?: "",
            isEditMode = true,
            itemId = itemToEdit?.id
        )
    }

    // 词汇管理对话框
    if (showManagementDialog && itemToManage != null) {
        VocabularyManagementDialog(
            vocabularyName = itemToManage?.name ?: "",
            sceneId = itemToManage?.id ?: 90, // 使用词汇表的ID作为sceneId
            onDismiss = {
                showManagementDialog = false
                itemToManage = null
            }
        )
    }

    // 删除确认对话框
    if (showDeleteDialog && itemToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteDialog = false
                itemToDelete = null
            },
            title = {
                Text(
                    text = "Delete Confirmation",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to delete \"${itemToDelete?.name}\"? This action cannot be undone.",
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        itemToDelete?.let { item ->
                            coroutineScope.launch {
                                deleteResult = VocabularyDeleteResult.Loading
                                val result = apiService.deleteVocabulary(item.id)
                                deleteResult = result

                                when (result) {
                                    is VocabularyDeleteResult.Success -> {
                                        Toast.makeText(context, "Delete successful", Toast.LENGTH_SHORT).show()
                                        // 刷新列表和下拉框数据
                                        performSearch()
                                        // 实时获取userId，确保使用当前登录用户的数据
                                        val userId = apiService.getCurrentUserId()
                                        allVocabularyResult = apiService.getAllVocabularyList(userId)
                                    }
                                    is VocabularyDeleteResult.Error -> {
                                        Toast.makeText(context, "Delete failed: ${result.message}", Toast.LENGTH_SHORT).show()
                                    }
                                    else -> {}
                                }

                                showDeleteDialog = false
                                itemToDelete = null
                            }
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFE53E3E)
                    )
                ) {
                    Text("Delete", color = Color.White)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        itemToDelete = null
                    }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}
