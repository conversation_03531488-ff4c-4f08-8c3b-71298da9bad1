package com.greenterp.data

import com.google.gson.annotations.SerializedName

/**
 * 词汇表列表请求参数
 */
data class VocabularyListRequest(
    @SerializedName("startPage")
    val startPage: Int,
    @SerializedName("pageNum")
    val pageNum: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("searchInfo")
    val searchInfo: String? = null
)

/**
 * 词汇表项目数据
 */
data class VocabularyData(
    @SerializedName("id")
    val id: Int,
    @SerializedName("createAt")
    val createAt: String,
    @SerializedName("updateAt")
    val updateAt: String?,
    @SerializedName("name")
    val name: String,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("sourceUserId")
    val sourceUserId: String?,
    @SerializedName("sourceSceneId")
    val sourceSceneId: Int,
    @SerializedName("shareUserIds")
    val shareUserIds: String?,
    @SerializedName("shareUserEmails")
    val shareUserEmails: String?
)

/**
 * 词汇表列表数据
 */
data class VocabularyListData(
    @SerializedName("data")
    val data: List<VocabularyData>,
    @SerializedName("count")
    val count: Int
)

/**
 * 词汇表列表响应
 */
data class VocabularyListResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: VocabularyListData
)

/**
 * 词汇表保存请求参数
 */
data class VocabularySaveRequest(
    @SerializedName("name")
    val name: String,
    @SerializedName("userId")
    val userId: String
)

/**
 * 词汇表更新请求参数
 */
data class VocabularyUpdateRequest(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("userId")
    val userId: String
)

/**
 * 词汇表保存响应
 */
data class VocabularySaveResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: VocabularyData
)

/**
 * 词汇表列表结果封装
 */
sealed class VocabularyListResult {
    data class Success(val vocabularyList: VocabularyListData) : VocabularyListResult()
    data class Error(val message: String) : VocabularyListResult()
    object Loading : VocabularyListResult()
}

/**
 * 获取所有词汇表请求参数
 */
data class VocabularyListAllRequest(
    @SerializedName("userId")
    val userId: String
)

/**
 * 获取所有词汇表响应
 */
data class VocabularyListAllResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: List<VocabularyData>
)

/**
 * 词汇表保存结果封装
 */
sealed class VocabularySaveResult {
    data class Success(val vocabulary: VocabularyData) : VocabularySaveResult()
    data class Error(val message: String) : VocabularySaveResult()
    object Loading : VocabularySaveResult()
}

/**
 * 获取所有词汇表结果封装
 */
sealed class VocabularyListAllResult {
    data class Success(val vocabularyList: List<VocabularyData>) : VocabularyListAllResult()
    data class Error(val message: String) : VocabularyListAllResult()
    object Loading : VocabularyListAllResult()
}

/**
 * 词汇表删除响应
 */
data class VocabularyDeleteResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 词汇表删除结果封装
 */
sealed class VocabularyDeleteResult {
    object Success : VocabularyDeleteResult()
    data class Error(val message: String) : VocabularyDeleteResult()
    object Loading : VocabularyDeleteResult()
}

/**
 * 词汇管理列表请求参数
 */
data class VocabularyManagementListRequest(
    @SerializedName("startPage")
    val startPage: Int,
    @SerializedName("pageNum")
    val pageNum: Int,
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("searchInfo")
    val searchInfo: String? = null
)

/**
 * 词汇管理项目数据
 */
data class VocabularyManagementData(
    @SerializedName("id")
    val id: Int,
    @SerializedName("createAt")
    val createAt: String,
    @SerializedName("updateAt")
    val updateAt: String?,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("email")
    val email: String?,
    @SerializedName("glossaryA")
    val glossaryA: String,
    @SerializedName("glossaryB")
    val glossaryB: String,
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("batchAddData")
    val batchAddData: String?
)

/**
 * 词汇管理列表数据
 */
data class VocabularyManagementListData(
    @SerializedName("data")
    val data: List<VocabularyManagementData>,
    @SerializedName("count")
    val count: Int
)

/**
 * 词汇管理列表响应
 */
data class VocabularyManagementListResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: VocabularyManagementListData
)

/**
 * 词汇管理列表结果封装
 */
sealed class VocabularyManagementListResult {
    data class Success(val vocabularyList: VocabularyManagementListData) : VocabularyManagementListResult()
    data class Error(val message: String) : VocabularyManagementListResult()
    object Loading : VocabularyManagementListResult()
}

/**
 * 词汇管理保存请求参数
 */
data class VocabularyManagementSaveRequest(
    @SerializedName("glossaryA")
    val glossaryA: String,
    @SerializedName("glossaryB")
    val glossaryB: String,
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("id")
    val id: Int? = null // 编辑模式下的ID
)

/**
 * 词汇管理保存响应
 */
data class VocabularyManagementSaveResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 词汇管理保存结果封装
 */
sealed class VocabularyManagementSaveResult {
    object Success : VocabularyManagementSaveResult()
    data class Error(val message: String) : VocabularyManagementSaveResult()
    object Loading : VocabularyManagementSaveResult()
}

/**
 * 词汇管理删除请求参数
 */
data class VocabularyManagementDeleteRequest(
    @SerializedName("ids")
    val ids: List<Int>,
    @SerializedName("userId")
    val userId: String
)

/**
 * 词汇管理删除响应
 */
data class VocabularyManagementDeleteResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 词汇管理删除结果封装
 */
sealed class VocabularyManagementDeleteResult {
    object Success : VocabularyManagementDeleteResult()
    data class Error(val message: String) : VocabularyManagementDeleteResult()
    object Loading : VocabularyManagementDeleteResult()
}

/**
 * 词汇管理导出请求参数
 */
data class VocabularyManagementExportRequest(
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("userId")
    val userId: String
)

/**
 * 词汇管理导出结果封装
 */
sealed class VocabularyManagementExportResult {
    data class Success(val fileBytes: ByteArray) : VocabularyManagementExportResult()
    data class Error(val message: String) : VocabularyManagementExportResult()
    object Loading : VocabularyManagementExportResult()
}

/**
 * 词汇管理导入响应
 */
data class VocabularyManagementImportResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 词汇管理导入结果封装
 */
sealed class VocabularyManagementImportResult {
    object Success : VocabularyManagementImportResult()
    data class Error(val message: String) : VocabularyManagementImportResult()
    object Loading : VocabularyManagementImportResult()
}

// ==================== 替换表相关数据模型 ====================

/**
 * 替换表项目数据
 */
data class ReplaceSceneData(
    @SerializedName("id")
    val id: Int,
    @SerializedName("createAt")
    val createAt: String,
    @SerializedName("updateAt")
    val updateAt: String?,
    @SerializedName("name")
    val name: String,
    @SerializedName("userId")
    val userId: String
)

/**
 * 替换表列表数据
 */
data class ReplaceSceneListData(
    @SerializedName("data")
    val data: List<ReplaceSceneData>,
    @SerializedName("count")
    val count: Int
)

/**
 * 替换表列表响应
 */
data class ReplaceSceneListResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: ReplaceSceneListData
)

/**
 * 替换表列表请求参数
 */
data class ReplaceSceneListRequest(
    @SerializedName("startPage")
    val startPage: Int,
    @SerializedName("pageNum")
    val pageNum: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("searchInfo")
    val searchInfo: String? = null
)

/**
 * 替换表列表结果
 */
sealed class ReplaceSceneListResult {
    object Loading : ReplaceSceneListResult()
    data class Success(val replaceSceneList: ReplaceSceneListData) : ReplaceSceneListResult()
    data class Error(val message: String) : ReplaceSceneListResult()
}

/**
 * 替换表保存请求参数
 */
data class ReplaceSceneSaveRequest(
    @SerializedName("name")
    val name: String,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("id")
    val id: Int? = null // 编辑模式下的ID
)

/**
 * 替换表保存响应数据
 */
data class ReplaceSceneSaveData(
    @SerializedName("id")
    val id: Int,
    @SerializedName("createAt")
    val createAt: String,
    @SerializedName("updateAt")
    val updateAt: String?,
    @SerializedName("name")
    val name: String,
    @SerializedName("userId")
    val userId: String
)

/**
 * 替换表保存响应
 */
data class ReplaceSceneSaveResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: ReplaceSceneSaveData
)

/**
 * 替换表保存结果
 */
sealed class ReplaceSceneSaveResult {
    object Loading : ReplaceSceneSaveResult()
    data class Success(val replaceScene: ReplaceSceneSaveData) : ReplaceSceneSaveResult()
    data class Error(val message: String) : ReplaceSceneSaveResult()
}

/**
 * 替换表删除响应
 */
data class ReplaceSceneDeleteResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 替换表删除结果
 */
sealed class ReplaceSceneDeleteResult {
    object Loading : ReplaceSceneDeleteResult()
    data class Success(val message: String) : ReplaceSceneDeleteResult()
    data class Error(val message: String) : ReplaceSceneDeleteResult()
}

/**
 * 替换表全部列表请求参数
 */
data class ReplaceSceneListAllRequest(
    @SerializedName("userId")
    val userId: String
)

/**
 * 替换表全部列表响应
 */
data class ReplaceSceneListAllResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: List<ReplaceSceneData>
)

/**
 * 替换表全部列表结果
 */
sealed class ReplaceSceneListAllResult {
    object Loading : ReplaceSceneListAllResult()
    data class Success(val replaceSceneList: List<ReplaceSceneData>) : ReplaceSceneListAllResult()
    data class Error(val message: String) : ReplaceSceneListAllResult()
}

// ==================== 替换表管理相关数据模型 ====================

/**
 * 替换表管理项目数据
 */
data class ReplaceManagementData(
    @SerializedName("id")
    val id: Int,
    @SerializedName("createAt")
    val createAt: String,
    @SerializedName("updateAt")
    val updateAt: String?,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("email")
    val email: String?,
    @SerializedName("replaceA")
    val replaceA: String,
    @SerializedName("replaceB")
    val replaceB: String,
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("batchAddData")
    val batchAddData: String?
)

/**
 * 替换表管理列表数据
 */
data class ReplaceManagementListData(
    @SerializedName("data")
    val data: List<ReplaceManagementData>,
    @SerializedName("count")
    val count: Int
)

/**
 * 替换表管理列表响应
 */
data class ReplaceManagementListResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: ReplaceManagementListData
)

/**
 * 替换表管理列表请求参数
 */
data class ReplaceManagementListRequest(
    @SerializedName("startPage")
    val startPage: Int,
    @SerializedName("pageNum")
    val pageNum: Int,
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("searchInfo")
    val searchInfo: String? = null
)

/**
 * 替换表管理列表结果
 */
sealed class ReplaceManagementListResult {
    object Loading : ReplaceManagementListResult()
    data class Success(val replaceList: ReplaceManagementListData) : ReplaceManagementListResult()
    data class Error(val message: String) : ReplaceManagementListResult()
}

/**
 * 替换表管理保存请求参数
 */
data class ReplaceManagementSaveRequest(
    @SerializedName("replaceA")
    val replaceA: String,
    @SerializedName("replaceB")
    val replaceB: String,
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("id")
    val id: Int? = null // 编辑模式下的ID
)

/**
 * 替换表管理保存响应
 */
data class ReplaceManagementSaveResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 替换表管理保存结果
 */
sealed class ReplaceManagementSaveResult {
    object Loading : ReplaceManagementSaveResult()
    object Success : ReplaceManagementSaveResult()
    data class Error(val message: String) : ReplaceManagementSaveResult()
}

/**
 * 替换表管理删除请求参数
 */
data class ReplaceManagementDeleteRequest(
    @SerializedName("ids")
    val ids: List<Int>,
    @SerializedName("userId")
    val userId: String
)

/**
 * 替换表管理删除响应
 */
data class ReplaceManagementDeleteResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 替换表管理删除结果
 */
sealed class ReplaceManagementDeleteResult {
    object Loading : ReplaceManagementDeleteResult()
    object Success : ReplaceManagementDeleteResult()
    data class Error(val message: String) : ReplaceManagementDeleteResult()
}

/**
 * 替换表管理导出请求参数
 */
data class ReplaceManagementExportRequest(
    @SerializedName("sceneId")
    val sceneId: Int,
    @SerializedName("userId")
    val userId: String
)

/**
 * 替换表管理导出结果封装
 */
sealed class ReplaceManagementExportResult {
    data class Success(val fileBytes: ByteArray) : ReplaceManagementExportResult()
    data class Error(val message: String) : ReplaceManagementExportResult()
    object Loading : ReplaceManagementExportResult()
}

/**
 * 替换表管理导入响应
 */
data class ReplaceManagementImportResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("time")
    val time: Long,
    @SerializedName("msg")
    val msg: String,
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 替换表管理导入结果封装
 */
sealed class ReplaceManagementImportResult {
    object Success : ReplaceManagementImportResult()
    data class Error(val message: String) : ReplaceManagementImportResult()
    object Loading : ReplaceManagementImportResult()
}
