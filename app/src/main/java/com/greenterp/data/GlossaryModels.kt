package com.greenterp.data

import com.google.gson.annotations.SerializedName

/**
 * 词汇表项数据模型
 */
data class GlossaryItem(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("createAt")
    val createAt: String,
    
    @SerializedName("updateAt")
    val updateAt: String?,
    
    @SerializedName("userId")
    val userId: String,
    
    @SerializedName("email")
    val email: String?,
    
    @SerializedName("glossaryA")
    val glossaryA: String,  // 原文
    
    @SerializedName("glossaryB")
    val glossaryB: String,  // 译文
    
    @SerializedName("sceneId")
    val sceneId: Int,
    
    @SerializedName("batchAddData")
    val batchAddData: String?
)

/**
 * 词汇表列表响应数据
 */
data class GlossaryListData(
    @SerializedName("data")
    val data: List<GlossaryItem>,
    
    @SerializedName("count")
    val count: Int
)

/**
 * 词汇表API响应
 */
data class GlossaryResponse(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("time")
    val time: Long,
    
    @SerializedName("msg")
    val msg: String,
    
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("data")
    val data: GlossaryListData
)

/**
 * 词汇表请求参数
 */
data class GlossaryRequest(
    @SerializedName("startPage")
    val startPage: Int = 0,
    
    @SerializedName("pageNum")
    val pageNum: Int = 9999999,
    
    @SerializedName("searchInfo")
    val searchInfo: String = "",
    
    @SerializedName("ids")
    val ids: List<Int>,
    
    @SerializedName("userId")
    val userId: String
)
