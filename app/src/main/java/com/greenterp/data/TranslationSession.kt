package com.greenterp.data

import java.util.UUID

/**
 * 翻译会话数据类
 */
data class TranslationSession(
    val id: String = UUID.randomUUID().toString(),
    val timestamp: String,
    val translationResults: List<TranslationResult> = emptyList()
)

/**
 * 翻译结果数据类
 */
data class TranslationResult(
    val originalText: String,
    val translatedText: String,
    val isIntermediate: Boolean = false,
    val index: Int = 0
)
