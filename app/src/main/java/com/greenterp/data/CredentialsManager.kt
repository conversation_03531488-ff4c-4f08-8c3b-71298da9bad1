package com.greenterp.data

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

/**
 * 登录凭据管理类
 * 使用加密的SharedPreferences安全存储用户的登录信息
 */
class CredentialsManager private constructor(context: Context) {
    private val sharedPreferences: SharedPreferences
    
    companion object {
        @Volatile
        private var INSTANCE: CredentialsManager? = null
        
        private const val PREFS_NAME = "login_credentials"
        private const val KEY_EMAIL = "saved_email"
        private const val KEY_PASSWORD = "saved_password"
        private const val KEY_REMEMBER_PASSWORD = "remember_password"
        
        fun getInstance(context: Context): CredentialsManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CredentialsManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    init {
        // 创建加密的SharedPreferences
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
            
        sharedPreferences = try {
            EncryptedSharedPreferences.create(
                context,
                PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )
        } catch (e: Exception) {
            // 如果加密失败，使用普通的SharedPreferences作为备选
            context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        }
    }
    
    /**
     * 保存登录凭据
     */
    fun saveCredentials(email: String, password: String, rememberPassword: Boolean) {
        sharedPreferences.edit()
            .putString(KEY_EMAIL, email)
            .putString(KEY_PASSWORD, if (rememberPassword) password else "")
            .putBoolean(KEY_REMEMBER_PASSWORD, rememberPassword)
            .apply()
    }
    
    /**
     * 获取保存的邮箱
     */
    fun getSavedEmail(): String {
        return sharedPreferences.getString(KEY_EMAIL, "") ?: ""
    }
    
    /**
     * 获取保存的密码
     */
    fun getSavedPassword(): String {
        return if (isRememberPasswordEnabled()) {
            sharedPreferences.getString(KEY_PASSWORD, "") ?: ""
        } else {
            ""
        }
    }
    
    /**
     * 检查是否启用了记住密码
     */
    fun isRememberPasswordEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_REMEMBER_PASSWORD, false)
    }
    
    /**
     * 清除保存的凭据
     */
    fun clearCredentials() {
        sharedPreferences.edit()
            .remove(KEY_EMAIL)
            .remove(KEY_PASSWORD)
            .putBoolean(KEY_REMEMBER_PASSWORD, false)
            .apply()
    }
    
    /**
     * 只清除密码，保留邮箱
     */
    fun clearPassword() {
        sharedPreferences.edit()
            .remove(KEY_PASSWORD)
            .putBoolean(KEY_REMEMBER_PASSWORD, false)
            .apply()
    }
}
