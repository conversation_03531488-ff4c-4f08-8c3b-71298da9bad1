package com.greenterp.data

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.greenterp.network.UserData

/**
 * 用户信息管理类
 */
class UserManager private constructor(context: Context) {
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
    private val gson = Gson()
    
    companion object {
        @Volatile
        private var INSTANCE: UserManager? = null
        
        fun getInstance(context: Context): UserManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UserManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 保存用户信息
     */
    fun saveUserData(userData: UserData) {
        val userJson = gson.toJson(userData)
        android.util.Log.d("UserManager", "保存用户数据: ${userData.emailAddress}")
        sharedPreferences.edit()
            .putString("user_data", userJson)
            .putBoolean("is_logged_in", true)
            .apply()
    }
    
    /**
     * 获取用户信息
     */
    fun getUserData(): UserData? {
        val userJson = sharedPreferences.getString("user_data", null)
        return if (userJson != null) {
            try {
                val userData = gson.fromJson(userJson, UserData::class.java)
                android.util.Log.d("UserManager", "获取用户数据: ${userData?.emailAddress}")
                userData
            } catch (e: Exception) {
                android.util.Log.e("UserManager", "解析用户数据失败", e)
                null
            }
        } else {
            android.util.Log.d("UserManager", "没有保存的用户数据")
            null
        }
    }
    
    /**
     * 获取用户ID
     */
    fun getUserId(): String? {
        return getUserData()?.userId
    }
    
    /**
     * 检查是否已登录
     */
    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean("is_logged_in", false) && getUserData() != null
    }
    
    /**
     * 清除用户信息（登出）
     */
    fun clearUserData() {
        sharedPreferences.edit()
            .remove("user_data")
            .putBoolean("is_logged_in", false)
            .apply()
    }
}
