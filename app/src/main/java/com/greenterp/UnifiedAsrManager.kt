
package com.greenterp

import android.content.Context
import android.util.Log
import com.greenterp.data.RecognitionResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 统一的ASR管理器
 * 支持多种语音识别服务的切换和管理
 */
class UnifiedAsrManager(private val context: Context) {
    
    companion object {
        private const val TAG = "UnifiedAsrManager"
    }
    
    // 当前使用的ASR服务类型标识
    private var currentServiceType: String = AsrServiceType.ONLINE_1.type
    
    // ASR管理器实例
    private var xfeiAsrManager: XfeiAsrManager? = null
    private var azureAsrManager: AzureAsrManager? = null
    
    // 统一的状态流
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

    private val _recognitionResults = MutableStateFlow<List<RecognitionResult>>(emptyList())
    val recognitionResults: StateFlow<List<RecognitionResult>> = _recognitionResults.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _currentSessionId = MutableStateFlow<String?>(null)
    val currentSessionId: StateFlow<String?> = _currentSessionId.asStateFlow()

    // 连接状态：idle, connecting, connected, error
    private val _connectionState = MutableStateFlow("idle")
    val connectionState: StateFlow<String> = _connectionState.asStateFlow()
    
    // 当前语言设置
    private var primaryLanguage: String = "English-United States"
    private var secondaryLanguage: String = "Chinese-Mandarin"
    private var currentLanguageIsPrimary: Boolean = true
    private var primaryServiceDisplayName: String = "Online-1"
    private var secondaryServiceDisplayName: String = "Online-1"

    // 协程作用域和任务
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private var observeJob: Job? = null
    
    init {
        // 初始化默认的ASR管理器
        initializeCurrentManager()
    }
    
    /**
     * 设置语音识别服务配置
     */
    fun setServiceConfiguration(
        primaryLang: String,
        secondaryLang: String,
        primaryService: String,
        secondaryService: String,
        isPrimary: Boolean
    ) {
        val oldLanguageIsPrimary = currentLanguageIsPrimary
        val oldServiceType = currentServiceType

        primaryLanguage = primaryLang
        secondaryLanguage = secondaryLang
        primaryServiceDisplayName = primaryService
        secondaryServiceDisplayName = secondaryService
        currentLanguageIsPrimary = isPrimary

        // 更新当前服务类型标识
        val currentServiceDisplayName = if (isPrimary) primaryService else secondaryService
        currentServiceType = LanguageConfig.getServiceTypeIdentifier(currentServiceDisplayName)

        Log.d(TAG, "设置服务配置: 主语言=$primaryLang($primaryService), 次语言=$secondaryLang($secondaryService), 当前使用主语言=$isPrimary, 服务类型=$currentServiceType")

        // 检查是否需要重新初始化（语言切换或服务类型变化）
        val needReinitialize = (oldLanguageIsPrimary != isPrimary) || (oldServiceType != currentServiceType)

        if (needReinitialize) {
            // 如果正在录音或连接中，先停止
            val wasRecording = _isRecording.value
            val wasConnecting = _connectionState.value == "connecting"

            if (wasRecording || wasConnecting) {
                Log.d(TAG, "语言切换时停止当前识别")
                stopRecognition()
                _connectionState.value = "idle"
            }

            // 重新初始化管理器
            initializeCurrentManager()
        }

        // 设置语言
        updateCurrentLanguage()
    }
    
    /**
     * 切换语言（已弃用 - 现在由MainActivity的LaunchedEffect统一处理）
     * 保留此方法以保持API兼容性，但实际逻辑已移至setServiceConfiguration
     */
    @Deprecated("语言切换现在由MainActivity的LaunchedEffect统一处理")
    fun toggleLanguage() {
        // 此方法现在为空，实际的语言切换逻辑在setServiceConfiguration中处理
        Log.d(TAG, "toggleLanguage() 被调用，但逻辑已移至setServiceConfiguration")
    }
    
    /**
     * 初始化当前的ASR管理器
     */
    private fun initializeCurrentManager() {
        // 释放之前的管理器
        releaseManagers()

        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> {
                // Online-1 使用Azure实现
                azureAsrManager = AzureAsrManager(context)
                observeAzureManager()
            }
            AsrServiceType.ONLINE_ZH_EN.type -> {
                // Online-ZH/EN only 使用讯飞实现
                xfeiAsrManager = XfeiAsrManager(context)
                observeXfeiManager()
            }
            else -> {
                // 默认使用Online-1 (Azure实现)
                azureAsrManager = AzureAsrManager(context)
                observeAzureManager()
            }
        }

        Log.d(TAG, "初始化ASR管理器: $currentServiceType")
    }
    
    /**
     * 更新当前语言设置
     */
    private fun updateCurrentLanguage() {
        val currentLang = if (currentLanguageIsPrimary) primaryLanguage else secondaryLanguage
        val currentServiceDisplayName = if (currentLanguageIsPrimary) primaryServiceDisplayName else secondaryServiceDisplayName
        val languageCode = LanguageConfig.getCurrentLanguageCode(currentLang, currentServiceDisplayName)

        Log.d(TAG, "更新语言设置: $currentLang -> $languageCode, 服务类型=$currentServiceType")

        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> {
                // Online-1 使用Azure实现
                azureAsrManager?.setLanguage(languageCode ?: "en-US")
            }
            AsrServiceType.ONLINE_ZH_EN.type -> {
                // Online-ZH/EN only 使用讯飞实现
                xfeiAsrManager?.setLanguage(languageCode ?: "en-US")
            }
            else -> {
                // 默认使用Azure实现
                azureAsrManager?.setLanguage(languageCode ?: "en-US")
            }
        }
    }
    
    /**
     * 观察讯飞ASR管理器状态
     */
    private fun observeXfeiManager() {
        observeJob?.cancel()
        xfeiAsrManager?.let { manager ->
            observeJob = coroutineScope.launch {
                launch {
                    manager.isRecording.collect {
                        _isRecording.value = it
                        if (it) {
                            _connectionState.value = "connected"
                        }
                    }
                }
                launch {
                    manager.recognitionResults.collect { _recognitionResults.value = it }
                }
                launch {
                    manager.errorMessage.collect {
                        _errorMessage.value = it
                        if (it != null) {
                            _connectionState.value = "error"
                        }
                    }
                }
                launch {
                    manager.isConnected.collect { _isConnected.value = it }
                }
                launch {
                    manager.currentSessionId.collect { _currentSessionId.value = it }
                }
            }
        }
    }

    /**
     * 观察Azure ASR管理器状态
     */
    private fun observeAzureManager() {
        observeJob?.cancel()
        azureAsrManager?.let { manager ->
            observeJob = coroutineScope.launch {
                launch {
                    manager.isRecording.collect {
                        _isRecording.value = it
                        if (it) {
                            _connectionState.value = "connected"
                        }
                    }
                }
                launch {
                    manager.recognitionResults.collect { _recognitionResults.value = it }
                }
                launch {
                    manager.errorMessage.collect {
                        _errorMessage.value = it
                        if (it != null) {
                            _connectionState.value = "error"
                        }
                    }
                }
                launch {
                    manager.isConnected.collect { _isConnected.value = it }
                }
                launch {
                    manager.currentSessionId.collect { _currentSessionId.value = it }
                }
            }
        }
    }
    
    /**
     * 检查录音权限
     */
    fun hasRecordPermission(): Boolean {
        return when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.hasRecordPermission() ?: false
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.hasRecordPermission() ?: false
            else -> azureAsrManager?.hasRecordPermission() ?: false
        }
    }

    /**
     * 开始语音识别
     */
    fun startRecognition(): Boolean {
        // 如果已经在连接或录音中，不允许重复启动
        if (_connectionState.value == "connecting" || _isRecording.value) {
            Log.w(TAG, "已经在连接或录音中，无法重复启动")
            return false
        }

        updateCurrentLanguage()

        // 设置连接状态为连接中
        _connectionState.value = "connecting"
        _errorMessage.value = null

        return when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> {
                azureAsrManager?.startRecognition() ?: false
            }
            AsrServiceType.ONLINE_ZH_EN.type -> {
                xfeiAsrManager?.startRecognition() ?: false
            }
            else -> {
                azureAsrManager?.startRecognition() ?: false
            }
        }
    }

    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        // 重置连接状态
        _connectionState.value = "idle"

        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.stopRecognition()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.stopRecognition()
            else -> azureAsrManager?.stopRecognition()
        }
    }

    /**
     * 清空识别结果
     */
    fun clearResults() {
        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.clearResults()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.clearResults()
            else -> azureAsrManager?.clearResults()
        }
    }

    /**
     * 清空错误信息
     */
    fun clearError() {
        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.clearError()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.clearError()
            else -> azureAsrManager?.clearError()
        }
    }
    
    /**
     * 获取当前识别结果
     */
    fun getCurrentResults(): List<RecognitionResult> {
        return when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.recognitionResults?.value ?: emptyList()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.recognitionResults?.value ?: emptyList()
            else -> azureAsrManager?.recognitionResults?.value ?: emptyList()
        }
    }
    
    /**
     * 释放所有管理器资源
     */
    private fun releaseManagers() {
        observeJob?.cancel()
        observeJob = null
        azureAsrManager?.release()
        azureAsrManager = null
        xfeiAsrManager = null
    }

    /**
     * 释放资源
     */
    fun release() {
        releaseManagers()
        Log.d(TAG, "统一ASR管理器资源已释放")
    }
}
