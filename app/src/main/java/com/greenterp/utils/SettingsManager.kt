package com.greenterp.utils

import android.content.Context
import android.content.SharedPreferences
import com.greenterp.network.TranslationModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 设置管理类 - 负责持久化保存用户设置
 */
class SettingsManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    companion object {
        private const val PREFS_NAME = "terpmeta_settings"
        
        // 设置键名
        private const val KEY_PRIMARY_LANGUAGE = "primary_language"
        private const val KEY_SECONDARY_LANGUAGE = "secondary_language"
        private const val KEY_CURRENT_LANGUAGE_IS_PRIMARY = "current_language_is_primary"
        private const val KEY_SELECTED_TRANSLATION_MODEL = "selected_translation_model"
        private const val KEY_FONT_SIZE = "font_size"
        private const val KEY_IS_DARK_THEME = "is_dark_theme"
        private const val KEY_SPLIT_RATIO = "split_ratio"
        private const val KEY_VOICE_SERVICE_TYPE = "voice_service_type"
        private const val KEY_PRIMARY_VOICE_SERVICE = "primary_voice_service"
        private const val KEY_SECONDARY_VOICE_SERVICE = "secondary_voice_service"
        private const val KEY_SELECTED_VOCABULARY_NAME = "selected_vocabulary_name"
        private const val KEY_SELECTED_VOCABULARY_ID = "selected_vocabulary_id"
        private const val KEY_SELECTED_REPLACE_NAME = "selected_replace_name"
        private const val KEY_SELECTED_REPLACE_ID = "selected_replace_id"
        private const val KEY_HIGHLIGHT_COLOR = "highlight_color"
    }
    
    // 语言设置
    var primaryLanguage: String
        get() = sharedPreferences.getString(KEY_PRIMARY_LANGUAGE, "English-United States") ?: "English-United States"
        set(value) = sharedPreferences.edit().putString(KEY_PRIMARY_LANGUAGE, value).apply()
    
    var secondaryLanguage: String
        get() = sharedPreferences.getString(KEY_SECONDARY_LANGUAGE, "Chinese-Mandarin") ?: "Chinese-Mandarin"
        set(value) = sharedPreferences.edit().putString(KEY_SECONDARY_LANGUAGE, value).apply()
    
    var currentLanguageIsPrimary: Boolean
        get() = sharedPreferences.getBoolean(KEY_CURRENT_LANGUAGE_IS_PRIMARY, true)
        set(value) = sharedPreferences.edit().putBoolean(KEY_CURRENT_LANGUAGE_IS_PRIMARY, value).apply()
    
    // 翻译模型设置
    var selectedTranslationModel: TranslationModel?
        get() {
            val json = sharedPreferences.getString(KEY_SELECTED_TRANSLATION_MODEL, null)
            return if (json != null) {
                try {
                    gson.fromJson(json, TranslationModel::class.java)
                } catch (e: Exception) {
                    null
                }
            } else {
                null
            }
        }
        set(value) {
            val json = if (value != null) gson.toJson(value) else null
            sharedPreferences.edit().putString(KEY_SELECTED_TRANSLATION_MODEL, json).apply()
        }
    
    // UI设置
    var fontSize: Int
        get() = sharedPreferences.getInt(KEY_FONT_SIZE, 16)
        set(value) = sharedPreferences.edit().putInt(KEY_FONT_SIZE, value).apply()
    
    var isDarkTheme: Boolean
        get() = sharedPreferences.getBoolean(KEY_IS_DARK_THEME, false)
        set(value) = sharedPreferences.edit().putBoolean(KEY_IS_DARK_THEME, value).apply()
    
    var splitRatio: Float
        get() = sharedPreferences.getFloat(KEY_SPLIT_RATIO, 0.6f)
        set(value) = sharedPreferences.edit().putFloat(KEY_SPLIT_RATIO, value).apply()

    var highlightColor: Int
        get() = sharedPreferences.getInt(KEY_HIGHLIGHT_COLOR, 0xFFFF5722.toInt()) // 默认橙红色
        set(value) = sharedPreferences.edit().putInt(KEY_HIGHLIGHT_COLOR, value).apply()

    // 语音服务设置
    var voiceServiceType: String
        get() = sharedPreferences.getString(KEY_VOICE_SERVICE_TYPE, "Azure") ?: "Azure"
        set(value) = sharedPreferences.edit().putString(KEY_VOICE_SERVICE_TYPE, value).apply()
    
    var primaryVoiceService: String
        get() = sharedPreferences.getString(KEY_PRIMARY_VOICE_SERVICE, "Online-1") ?: "Online-1"
        set(value) = sharedPreferences.edit().putString(KEY_PRIMARY_VOICE_SERVICE, value).apply()
    
    var secondaryVoiceService: String
        get() = sharedPreferences.getString(KEY_SECONDARY_VOICE_SERVICE, "Online-1") ?: "Online-1"
        set(value) = sharedPreferences.edit().putString(KEY_SECONDARY_VOICE_SERVICE, value).apply()

    // 词汇表选择相关设置
    var selectedVocabularyName: String
        get() = sharedPreferences.getString(KEY_SELECTED_VOCABULARY_NAME, "") ?: ""
        set(value) = sharedPreferences.edit().putString(KEY_SELECTED_VOCABULARY_NAME, value).apply()

    var selectedVocabularyId: Int
        get() = sharedPreferences.getInt(KEY_SELECTED_VOCABULARY_ID, -1)
        set(value) = sharedPreferences.edit().putInt(KEY_SELECTED_VOCABULARY_ID, value).apply()

    // 替换表选择相关设置
    var selectedReplaceName: String
        get() = sharedPreferences.getString(KEY_SELECTED_REPLACE_NAME, "") ?: ""
        set(value) = sharedPreferences.edit().putString(KEY_SELECTED_REPLACE_NAME, value).apply()

    var selectedReplaceId: Int
        get() = sharedPreferences.getInt(KEY_SELECTED_REPLACE_ID, -1)
        set(value) = sharedPreferences.edit().putInt(KEY_SELECTED_REPLACE_ID, value).apply()

    /**
     * 清空所有设置
     */
    fun clearAllSettings() {
        sharedPreferences.edit().clear().apply()
    }
    
    /**
     * 导出设置为JSON字符串
     */
    fun exportSettings(): String {
        val settings = mapOf(
            KEY_PRIMARY_LANGUAGE to primaryLanguage,
            KEY_SECONDARY_LANGUAGE to secondaryLanguage,
            KEY_CURRENT_LANGUAGE_IS_PRIMARY to currentLanguageIsPrimary,
            KEY_SELECTED_TRANSLATION_MODEL to selectedTranslationModel,
            KEY_FONT_SIZE to fontSize,
            KEY_IS_DARK_THEME to isDarkTheme,
            KEY_SPLIT_RATIO to splitRatio,
            KEY_VOICE_SERVICE_TYPE to voiceServiceType,
            KEY_PRIMARY_VOICE_SERVICE to primaryVoiceService,
            KEY_SECONDARY_VOICE_SERVICE to secondaryVoiceService
        )
        return gson.toJson(settings)
    }
    
    /**
     * 从JSON字符串导入设置
     */
    fun importSettings(json: String): Boolean {
        return try {
            val type = object : TypeToken<Map<String, Any>>() {}.type
            val settings: Map<String, Any> = gson.fromJson(json, type)
            
            settings[KEY_PRIMARY_LANGUAGE]?.let { primaryLanguage = it.toString() }
            settings[KEY_SECONDARY_LANGUAGE]?.let { secondaryLanguage = it.toString() }
            settings[KEY_CURRENT_LANGUAGE_IS_PRIMARY]?.let { currentLanguageIsPrimary = it as Boolean }
            settings[KEY_FONT_SIZE]?.let { fontSize = (it as Double).toInt() }
            settings[KEY_IS_DARK_THEME]?.let { isDarkTheme = it as Boolean }
            settings[KEY_SPLIT_RATIO]?.let { splitRatio = (it as Double).toFloat() }
            settings[KEY_VOICE_SERVICE_TYPE]?.let { voiceServiceType = it.toString() }
            settings[KEY_PRIMARY_VOICE_SERVICE]?.let { primaryVoiceService = it.toString() }
            settings[KEY_SECONDARY_VOICE_SERVICE]?.let { secondaryVoiceService = it.toString() }
            
            true
        } catch (e: Exception) {
            false
        }
    }
}
