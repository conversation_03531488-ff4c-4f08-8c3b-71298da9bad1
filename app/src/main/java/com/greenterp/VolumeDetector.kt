package com.greenterp

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.math.sqrt

/**
 * 音量检测器
 * 独立于ASR服务，专门用于检测麦克风音量变化
 */
class VolumeDetector {
    companion object {
        private const val TAG = "VolumeDetector"
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE_FACTOR = 2
        private const val DETECTION_INTERVAL = 50L // 50ms检测一次
    }

    private var audioRecord: AudioRecord? = null
    private var isDetectingInternal = false
    private var detectionJob: Job? = null

    // 音量状态流
    private val _volumeLevel = MutableStateFlow(0f)
    val volumeLevel: StateFlow<Float> = _volumeLevel

    // 是否正在检测状态流
    private val _isDetecting = MutableStateFlow(false)
    val isDetecting: StateFlow<Boolean> = _isDetecting

    /**
     * 开始音量检测
     */
    fun startDetection(): Boolean {
        if (isDetectingInternal) {
            Log.w(TAG, "音量检测已经在运行中")
            return false
        }

        val bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT) * BUFFER_SIZE_FACTOR
        
        try {
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            )
            
            if (audioRecord?.state == AudioRecord.STATE_INITIALIZED) {
                audioRecord?.startRecording()
                isDetectingInternal = true
                _isDetecting.value = true

                detectionJob = CoroutineScope(Dispatchers.IO).launch {
                    detectVolume()
                }

                Log.d(TAG, "音量检测已启动")
                return true
            } else {
                Log.e(TAG, "AudioRecord初始化失败")
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动音量检测失败", e)
            return false
        }
    }

    /**
     * 停止音量检测
     */
    fun stopDetection() {
        if (!isDetectingInternal) {
            Log.w(TAG, "音量检测未在运行")
            return
        }

        isDetectingInternal = false
        _isDetecting.value = false
        detectionJob?.cancel()
        
        // 停止录音
        audioRecord?.apply {
            if (state == AudioRecord.STATE_INITIALIZED) {
                stop()
            }
            release()
        }
        audioRecord = null
        
        // 重置音量为0
        _volumeLevel.value = 0f
        
        Log.d(TAG, "音量检测已停止")
    }

    /**
     * 检测音量
     */
    private suspend fun detectVolume() {
        val bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT)
        val buffer = ByteArray(bufferSize)
        
        while (isDetectingInternal && audioRecord != null) {
            try {
                val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (bytesRead > 0) {
                    val volume = calculateVolume(buffer, bytesRead)
                    _volumeLevel.value = volume
                }
                
                // 控制检测频率
                delay(DETECTION_INTERVAL)
            } catch (e: Exception) {
                Log.e(TAG, "音量检测过程中出错", e)
                break
            }
        }
    }

    /**
     * 计算音频音量
     * @param buffer 音频数据缓冲区
     * @param bytesRead 实际读取的字节数
     * @return 音量级别 (0.0 - 1.0)
     */
    private fun calculateVolume(buffer: ByteArray, bytesRead: Int): Float {
        var sum = 0.0
        var sampleCount = 0
        
        // 处理16位PCM数据
        for (i in 0 until bytesRead step 2) {
            if (i + 1 < bytesRead) {
                // 将两个字节组合成一个16位的样本（小端序）
                val sample = ((buffer[i + 1].toInt() shl 8) or (buffer[i].toInt() and 0xFF)).toShort()
                sum += sample * sample
                sampleCount++
            }
        }
        
        if (sampleCount == 0) return 0f
        
        // 计算RMS (Root Mean Square)
        val rms = sqrt(sum / sampleCount)
        
        // 归一化到0-1范围，32768是16位音频的最大值
        val normalizedVolume = (rms / 32768.0).coerceIn(0.0, 1.0)
        
        // 应用对数缩放，使音量变化更明显
        return if (normalizedVolume > 0.001) {
            // 使用平方根来增强小音量的可见性
            kotlin.math.sqrt(normalizedVolume.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopDetection()
    }
}
