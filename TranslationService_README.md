# 翻译服务使用说明

## 概述

基于JavaScript代码实现的Kotlin版本翻译服务，支持多种翻译API：
- 微软翻译 (type = 2)
- Google翻译 (type = 3)  
- Google Chrome扩展翻译 (type = 4)

## 主要文件

1. **ApiService.kt** - 核心翻译服务类
2. **LoginModels.kt** - 数据模型定义
3. **TranslationServiceExample.kt** - 基础调用示例
4. **TranslationUsageExample.kt** - Activity中的使用示例

## 核心功能

### 1. 获取翻译模型列表

```kotlin
val apiService = ApiService.getInstance()

lifecycleScope.launch {
    when (val result = apiService.getTranslationModelList()) {
        is TranslationModelListResult.Success -> {
            val models = result.response.data
            // 使用翻译模型列表
        }
        is TranslationModelListResult.Error -> {
            // 处理错误
        }
        else -> {
            // 加载中状态
        }
    }
}
```

### 2. 智能翻译（自动选择翻译服务）

```kotlin
val request = TranslateSearchRequest(
    text = "Hello, how are you?",
    source_lang = "en-US",
    target_lang = "zh-CN",
    translateApi = translationModel // 从模型列表中获取
)

when (val result = apiService.translateSearch(request)) {
    is TranslateSearchResult.Success -> {
        val translatedText = result.response.data
        // 使用翻译结果
    }
    is TranslateSearchResult.Error -> {
        // 处理错误
    }
}
```

### 3. 指定翻译服务

#### 微软翻译
```kotlin
val result = apiService.microsoftTranslateSearch(request)
```

#### Google翻译
```kotlin
val result = apiService.googleTranslateSearch(request)
```

#### Google Chrome扩展翻译
```kotlin
val result = apiService.googleTranslateSearchByChromeExtension(request)
```

## 数据模型

### TranslationModel
```kotlin
data class TranslationModel(
    val id: Int,
    val type: Int,                    // 2=微软, 3=Google, 4=Chrome扩展
    val name: String,
    val enable: Int,                  // 1=启用, 0=禁用
    val microsoftKey: String,         // 微软翻译API密钥
    val googleNokeyUrl: String,       // Google翻译URL
    val googleExtensionUrl: String,   // Chrome扩展翻译URL
    val googleExtensionKey: String,   // Chrome扩展密钥
    val googleXREFKey: String,        // Chrome扩展Referer
    // ... 其他字段
)
```

### TranslateSearchRequest
```kotlin
data class TranslateSearchRequest(
    val text: String,           // 要翻译的文本
    val source_lang: String,    // 源语言 (如: "en-US")
    val target_lang: String,    // 目标语言 (如: "zh-CN")
    val translateApi: TranslationModel  // 翻译模型配置
)
```

### TranslateSearchResponse
```kotlin
data class TranslateSearchResponse(
    val code: Int,    // 响应码 (200表示成功)
    val data: String  // 翻译结果文本
)
```

## 特殊功能

### 1. 自动故障转移
- 当微软翻译使用cymo密钥失败时，自动切换到GT备用密钥
- 全局变量`useGT`控制是否使用备用密钥

### 2. 语言代码处理
- 自动处理语言代码格式 (如: "en-US" -> "en")
- 支持完整的语言区域代码

### 3. UUID生成
- 微软翻译请求自动生成X-ClientTraceId
- 使用Java标准UUID.randomUUID()

## 错误处理

所有翻译方法都返回`TranslateSearchResult`密封类：

```kotlin
sealed class TranslateSearchResult {
    object Loading : TranslateSearchResult()
    data class Success(val response: TranslateSearchResponse) : TranslateSearchResult()
    data class Error(val message: String) : TranslateSearchResult()
}
```

## 使用示例

### 在Activity中使用

```kotlin
class MainActivity : ComponentActivity() {
    private val apiService = ApiService.getInstance()
    
    private fun translateText(text: String) {
        lifecycleScope.launch {
            // 1. 获取翻译模型
            val modelResult = apiService.getTranslationModelList()
            if (modelResult is TranslationModelListResult.Success) {
                val model = modelResult.response.data.firstOrNull { it.enable == 1 }
                
                if (model != null) {
                    // 2. 创建翻译请求
                    val request = TranslateSearchRequest(
                        text = text,
                        source_lang = "en-US",
                        target_lang = "zh-CN",
                        translateApi = model
                    )
                    
                    // 3. 执行翻译
                    when (val result = apiService.translateSearch(request)) {
                        is TranslateSearchResult.Success -> {
                            // 显示翻译结果
                            showTranslationResult(result.response.data)
                        }
                        is TranslateSearchResult.Error -> {
                            // 显示错误信息
                            showError(result.message)
                        }
                        else -> {
                            // 显示加载状态
                            showLoading()
                        }
                    }
                }
            }
        }
    }
}
```

### 语音识别结果翻译

```kotlin
fun onSpeechRecognitionResult(recognizedText: String) {
    translateText(
        text = recognizedText,
        onSuccess = { translatedText ->
            displayTranslationResult(recognizedText, translatedText)
        },
        onError = { errorMessage ->
            showTranslationError(errorMessage)
        }
    )
}
```

## 注意事项

1. **网络权限**: 确保应用有网络访问权限
2. **协程使用**: 所有翻译方法都是suspend函数，需要在协程中调用
3. **API密钥**: 确保翻译模型中配置了正确的API密钥
4. **错误处理**: 建议对所有翻译请求进行适当的错误处理
5. **语言代码**: 使用标准的语言代码格式 (如: "en-US", "zh-CN")

## 依赖项

项目已包含以下依赖：
- OkHttp3 (网络请求)
- Gson (JSON解析)
- Kotlin协程 (异步处理)

## 扩展性

可以通过以下方式扩展翻译服务：
1. 添加新的翻译API类型
2. 在`translateSearch`方法中添加新的case分支
3. 实现对应的翻译方法
4. 更新数据模型以支持新的配置参数
